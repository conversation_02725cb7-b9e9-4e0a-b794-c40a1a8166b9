import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from 'next/font/google';
import './globals.css';
import ReactQueryProvider from '@/api-requests/react-query-provider';
import { JotaiProvider } from '@/store/jobtai-provider';
import 'react-quill-new/dist/quill.snow.css';
import AuthProvider from '../providers/auth';
import { Toaster } from 'react-hot-toast';
import { getServerSession } from '@/lib/auth/get-session';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Talent',
  description:
    'No more ghosted applications. Land better jobs through job simulations.',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const session = await getServerSession();

  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ReactQueryProvider>
          <JotaiProvider>
            <AuthProvider session={session}>
              {children}
              <Toaster containerClassName="toast__container !z-[99999]" />
            </AuthProvider>
          </JotaiProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
