/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';
import { AT_COOKIE, isProd, maxAgeDays, RT_COOKIE } from '../../_utils';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { data } = await axiosInstance.post(
      API_ENDPONTS.SIGN_UP_VERIFY,
      body
    );
    const { accessToken, refreshToken, user } = data || {};

    // set cookies
    const res = NextResponse.json({ user });
    if (accessToken) {
      res.cookies.set(AT_COOKIE, accessToken, {
        httpOnly: false,
        sameSite: 'lax',
        secure: isProd,
        path: '/',
        maxAge: maxAgeDays(1),
      });
    }
    if (refreshToken) {
      res.cookies.set(RT_<PERSON>OKIE, refreshToken, {
        httpOnly: true,
        sameSite: 'lax',
        secure: isProd,
        path: '/',
        maxAge: maxAgeDays(7),
      });
    }

    return res;
  } catch (e: any) {
    return NextResponse.json(e?.response?.data ?? { message: 'error' }, {
      status: e?.response?.status ?? 500,
    });
  }
}
