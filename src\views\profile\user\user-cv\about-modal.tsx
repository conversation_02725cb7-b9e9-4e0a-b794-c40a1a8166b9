'use client';

import CloseIcon from '@/views/icons/close';
import { useState } from 'react';
import { ActionIcon, Button, Modal, Textarea, Title } from 'rizzui';

interface IProps {
  open: boolean;
  isUpdating: boolean;
  initValue?: string;
  onClose: () => void;
  onSave: (data: string) => Promise<boolean>;
}

export default function AboutModal({
  open,
  isUpdating,
  initValue,
  onClose,
  onSave,
}: IProps) {
  const [value, setValue] = useState(initValue || '');

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      size="lg"
      containerClassName="md:w-[800px]"
    >
      <div className="w-full p-6">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">Edit about</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-4">
          <div>
            {/* <FieldLabel title="First name" /> */}
            <Textarea
              value={value}
              onChange={(e) => setValue(e.target.value)}
              variant="flat"
              rows={10}
              placeholder="Write about your years of experience, industry, or skills"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-3">
          <Button
            className="bg-primary text-white"
            onClick={async () => {
              if (await onSave(value)) {
                onClose();
              }
            }}
            disabled={isUpdating}
          >
            {isUpdating ? 'Saving...' : 'Save'}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
