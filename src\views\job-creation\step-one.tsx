'use client';

import {
  Input,
  Radio,
  RadioGroup,
  Checkbox,
  MultiSelectOption,
  MultiSelect,
  Text,
  Select,
  Switch,
} from 'rizzui';
import { TogglePills } from './toggle-pills';
import RangeSlider from '../range-slider';
import FieldLabel from './field-label';
import cn from '@/utils/class-names';
import { FormValues } from './index';
import { Controller, useFormContext } from 'react-hook-form';
import {
  applyModeOptions,
  categoryOptions,
  employementOptions,
  levelOptions,
  workplaceOptions,
} from './options';
import CloseIcon from '../icons/close';
import { useMemo, useState } from 'react';
import countriesData from '@/data/countries.json';

const employmentToArray = (employment: any) => {
  const result: string[] = [];
  if (employment?.isFullTime) result.push('full_time');
  if (employment?.isPartTime) result.push('part_time');
  if (employment?.hasContract) result.push('contract');
  return result;
};

const arrayToEmployment = (array: string[]) => ({
  isFullTime: array.includes('full_time'),
  isPartTime: array.includes('part_time'),
  hasContract: array.includes('contract'),
});

const workplaceToArray = (workplace: any) => {
  const result: string[] = [];
  if (workplace?.isOnSite) result.push('onsite');
  if (workplace?.isRemote) result.push('remote');
  if (workplace?.isHybrid) result.push('hybrid');
  return result;
};

const arrayToWorkplace = (array: string[]) => ({
  isOnSite: array.includes('onsite'),
  isRemote: array.includes('remote'),
  isHybrid: array.includes('hybrid'),
});

function renderOptionDisplayValue(
  option: MultiSelectOption,
  selected: boolean
) {
  return (
    <div
      className="flex w-full cursor-pointer items-center justify-between gap-2 py-1"
      onClick={(e) => {
        e.currentTarget.dispatchEvent(
          new MouseEvent('mousedown', { bubbles: true })
        );
      }}
    >
      <div className="flex items-center gap-1">
        <span className="text-sm">{option.label}</span>
        {option.description && (
          <span className="text-xs text-gray-500">({option.description})</span>
        )}
      </div>
      <Checkbox
        variant="flat"
        size="sm"
        checked={selected}
        onClick={(e) => {
          e.stopPropagation();
          e.currentTarget.dispatchEvent(
            new MouseEvent('mousedown', { bubbles: true })
          );
        }}
        readOnly
      />
    </div>
  );
}

function renderDisplayValue(
  selectedItems: string[],
  options: MultiSelectOption[],
  handleClearItem: (value: string) => void,
  title: string
) {
  const filteredItems =
    options.filter((option) => selectedItems.includes(option.value)) || [];
  const isLongerThanThree = filteredItems.length > 3;

  return (
    <div className={cn('flex w-full flex-wrap items-center text-start')}>
      <div>{title}</div>
      {isLongerThanThree ? (
        <span className="border-muted ms-2 border border-s ps-2">
          {filteredItems.length} Selected
        </span>
      ) : (
        <div className="border-muted ms-2 flex items-center gap-2 border-s ps-2">
          {filteredItems.map((item, idx) => (
            <div
              className="border-muted flex items-center gap-2 rounded-md border ps-2"
              key={idx}
            >
              <Text fontWeight="medium">{item.label}</Text>
              <span
                className="hover:bg-muted cursor-pointer rounded-full p-1"
                onClick={(e) => {
                  e.stopPropagation();
                  handleClearItem(item.value);
                }}
              >
                <CloseIcon className="size-4" />
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

function GroupedChips({
  title,
  items,
  onRemoveValue,
  onClear,
  className,
}: {
  title: string;
  items: string[];
  onRemoveValue: (value: string) => void;
  onClear: () => void;
  className?: string;
}) {
  if (!items.length) return null;
  return (
    <div
      className={cn(
        'flex items-center gap-2 rounded-md border border-slate-200 px-2 py-1',
        className
      )}
    >
      {title && (
        <span className="select-none whitespace-nowrap rounded-md bg-slate-100 px-2 py-0.5 text-xs font-medium text-slate-600">
          {title}
        </span>
      )}

      <div
        className="no-scrollbar flex max-w-full flex-wrap items-center gap-2 overflow-x-auto"
        style={{ scrollbarWidth: 'none' }}
      >
        {items.map((chip) => (
          <span
            key={chip}
            className="group inline-flex items-center gap-1 rounded-md border border-slate-200 bg-white/80 px-2 py-0.5 text-[12px] leading-5 shadow-sm"
          >
            <span className="max-w-[12rem] truncate">{chip}</span>
            <button
              onClick={() => onRemoveValue(chip)}
              className="-mr-1 rounded-full p-0.5 text-slate-400 hover:bg-slate-100 hover:text-slate-600"
              aria-label={`Remove ${chip}`}
            >
              <CloseIcon className="h-3 w-3" />
            </button>
          </span>
        ))}
      </div>
      <button
        onClick={onClear}
        className="ml-1 rounded-full p-1 text-slate-400 hover:bg-slate-100 hover:text-slate-700"
        aria-label={`Clear ${title}`}
      >
        <CloseIcon className="h-4 w-4" />
      </button>
    </div>
  );
}

export default function StepOne() {
  const [skill, setSkill] = useState<string>('');
  const [locationSwitch, setLocationSwitch] = useState<boolean>(true);

  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<FormValues>();

  const salary = watch('salary');
  const categories = watch('categories');
  const levels = watch('levels');
  const skills = watch('skills') || [];

  const countryOptions = useMemo(
    () =>
      countriesData.map((country) => ({
        label: country.name,
        value: country.code,
      })),
    []
  );

  return (
    <>
      <div className="space-y-4">
        <div>
          <div className="text-lg">Add basics information</div>
          <div className="text-sm text-gray-500">
            Please fill in basic information for your job
          </div>
        </div>

        {/* Job title */}
        <div>
          <FieldLabel title="Job title" />
          <Controller
            name="title"
            control={control}
            rules={{
              required: 'Please enter job title',
              maxLength: {
                value: 120,
                message: 'Job title name cannot exceed 120 characters',
              },
            }}
            render={({ field }) => (
              <Input
                {...field}
                variant="flat"
                placeholder="Enter a Job title (e.g. Software Developer)"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
              />
            )}
          />
          {errors.title && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.title.message}
            </Text>
          )}
        </div>

        {/* Job category */}
        <div>
          <FieldLabel title="Job category" />
          <Controller
            name="categories"
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <MultiSelect
                {...field}
                clearable
                value={field.value || []}
                onChange={(e) => field.onChange(e)}
                onClear={() => field.onChange([])}
                options={categoryOptions}
                placeholder="Select category"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-multi-select-button]:!border-0 [&_.rizzui-multi-select-button]:!border-none [&_.rizzui-multi-select-button]:!outline-none [&_.rizzui-multi-select-button]:!ring-0"
                getOptionDisplayValue={renderOptionDisplayValue}
                displayValue={(selectedItems, options, handleClearItem) =>
                  renderDisplayValue(
                    selectedItems,
                    options,
                    handleClearItem || (() => {}),
                    'Category'
                  )
                }
              />
            )}
          />
          <GroupedChips
            className="mt-2"
            title=""
            items={categories || []}
            onRemoveValue={(value) => {
              const newCategories = (categories || []).filter(
                (cat) => cat !== value
              );
              setValue('categories', newCategories);
            }}
            onClear={() => {
              setValue('categories', []);
            }}
          />
        </div>

        <div>
          <FieldLabel title="Job level" />
          <Controller
            name="levels"
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <MultiSelect
                {...field}
                clearable
                onClear={() => field.onChange([])}
                options={levelOptions}
                placeholder="Select level"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-multi-select-button]:!border-0 [&_.rizzui-multi-select-button]:!border-none [&_.rizzui-multi-select-button]:!outline-none [&_.rizzui-multi-select-button]:!ring-0"
                getOptionDisplayValue={renderOptionDisplayValue}
                displayValue={(selectedItems, options, handleClearItem) =>
                  renderDisplayValue(
                    selectedItems,
                    options,
                    handleClearItem || (() => {}),
                    'Level'
                  )
                }
              />
            )}
          />
          <GroupedChips
            className="mt-2"
            title=""
            items={levels || []}
            onRemoveValue={(value) => {
              const newLevels = (levels || []).filter((lev) => lev !== value);
              setValue('levels', newLevels);
            }}
            onClear={() => {
              setValue('levels', []);
            }}
          />
        </div>

        <div>
          <FieldLabel title="Add skills" content="Press Enter to add skill" />
          <Controller
            name="skills"
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <Input
                value={skill}
                onChange={(e) => setSkill(e.target.value)}
                variant="flat"
                placeholder="Enter a skill and press Enter"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && skill.trim()) {
                    e.preventDefault();
                    field.onChange([...field.value, skill.trim()]);
                    setSkill('');
                  }
                }}
              />
            )}
          />
          <div className="mt-3 flex flex-wrap gap-3">
            {skills.map((s, index) => (
              <span
                key={index}
                className="inline-flex h-8 items-center gap-1 rounded-md px-3 text-sm shadow-[0_4px_30px_rgba(0,0,0,0.15)]"
              >
                <button
                  type="button"
                  onClick={() =>
                    setValue(
                      'skills',
                      skills.filter((_, i) => i !== index)
                    )
                  }
                  className="rounded-full text-gray-500 hover:bg-gray-200 hover:text-gray-700"
                >
                  <CloseIcon className="h-4 w-4 rounded-full" />
                </button>
                {s}
              </span>
            ))}
          </div>
        </div>

        <div>
          <FieldLabel title="Workplace location" />
          <Switch
            variant="flat"
            label={'Use company location'}
            checked={locationSwitch}
            onChange={(e) => setLocationSwitch(e.target.checked)}
          />
        </div>

        {!locationSwitch && (
          <>
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <FieldLabel title="Address" />
                <Controller
                  name="address"
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      variant="flat"
                      placeholder="Enter your address"
                      className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                      inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                    />
                  )}
                />
              </div>

              <div>
                <FieldLabel title="City" />
                <Controller
                  name="city"
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      variant="flat"
                      placeholder="Enter your city"
                      className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                      inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                    />
                  )}
                />
              </div>
            </div>

            {/* Region + Country */}
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <FieldLabel title="Region" />
                <Controller
                  name="region"
                  control={control}
                  rules={{
                    required: false,
                  }}
                  render={({ field }) => (
                    <Input
                      {...field}
                      variant="flat"
                      placeholder="Enter your region"
                      className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                      inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                    />
                  )}
                />
              </div>

              <div>
                <FieldLabel title="Country" />
                <Controller
                  name="country"
                  control={control}
                  rules={{
                    required: 'Please select your country',
                  }}
                  render={({ field }) => (
                    <Select
                      clearable
                      {...field}
                      onClear={() => field.onChange(null)}
                      options={countryOptions}
                      placeholder="Select your country"
                      className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                      searchable={true}
                    />
                  )}
                />
                {errors.country && (
                  <Text as="p" className="mt-0.5 text-xs text-red-600">
                    {errors.country.message}
                  </Text>
                )}
              </div>
            </div>
          </>
        )}

        <div>
          <FieldLabel
            title="Apply mode"
            content="Choose how candidates can apply for this position"
          />
          <Controller
            name="applyMode"
            control={control}
            render={({ field }) => (
              <TogglePills
                value={field.value as string[]}
                onChange={field.onChange}
                options={applyModeOptions}
              />
            )}
          />
        </div>

        <div>
          <FieldLabel
            title="Employment type"
            content="Select the type of employment for this position"
          />
          <Controller
            name="employment"
            control={control}
            render={({ field }) => (
              <TogglePills
                value={employmentToArray(field.value)}
                onChange={(array) => field.onChange(arrayToEmployment(array))}
                options={employementOptions}
              />
            )}
          />
        </div>

        {/* Workplace option */}
        <div>
          <FieldLabel
            title="Workplace option"
            content="Select where employees will work (on-site, remote, or hybrid arrangements)"
          />
          <Controller
            name="workplace"
            control={control}
            render={({ field }) => (
              <TogglePills
                value={workplaceToArray(field.value)}
                onChange={(array) => field.onChange(arrayToWorkplace(array))}
                options={workplaceOptions}
              />
            )}
          />
        </div>

        {/* <hr className="text-[#222222]" /> */}

        {/* <div>
          <div className="text-lg">Payment information</div>
          <div className="text-sm text-gray-500">
            Customize payment information
          </div>
        </div> */}

        {/* <div>
          <FieldLabel title="Pay type" />
          <Controller
            name="salary.period"
            control={control}
            render={({ field }) => (
              <RadioGroup
                value={field.value as string}
                setValue={(val) => {
                  field.onChange(val);
                }}
                className="flex flex-col gap-3"
              >
                <Radio
                  label="Hourly rate"
                  value="hour"
                  size="sm"
                  className="[&_.rizzui-radio-label]:!text-base"
                />
                <Radio
                  label="Monthly salary"
                  value="month"
                  size="sm"
                  className="[&_.rizzui-radio-label]:!text-base"
                />
                <Radio
                  label="Annual salary"
                  value="year"
                  size="sm"
                  className="[&_.rizzui-radio-label]:!text-base"
                />
              </RadioGroup>
            )}
          />
        </div> */}

        {/* <div>
          <FieldLabel title="Compensation range (USD)" />
          <div className="flex justify-between text-sm text-gray-400">
            <span>0</span>
            <span>100,000</span>
          </div>
          <Controller
            name="salary"
            control={control}
            render={({ field }) => (
              <RangeSlider
                range
                step={100}
                min={0}
                max={100000}
                onChange={(val) => {
                  const [min, max] = val as [number, number];
                  field.onChange({
                    ...field.value,
                    min,
                    max,
                  });
                }}
                value={[field.value?.min || 0, field.value?.max || 100000]}
              />
            )}
          />
          <div className="flex justify-between">
            <span>{(salary?.min as number).toLocaleString()}</span>
            <span>{(salary?.max as number).toLocaleString()}</span>
          </div>
        </div> */}
      </div>
    </>
  );
}
