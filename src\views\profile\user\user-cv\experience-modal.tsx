'use client';

import { UserCVExperience } from '@/store/user-atom';
import CloseIcon from '@/views/icons/close';
import FieldLabel from '@/views/job-creation/field-label';
import { useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  ActionIcon,
  Button,
  Input,
  Modal,
  Select,
  Textarea,
  Title,
} from 'rizzui';

interface IProps {
  open: boolean;
  isUpdating: boolean;
  initialData?: UserCVExperience & { index: number };
  onClose: () => void;
  onSave?: (data: UserCVExperience) => boolean | Promise<boolean>;
}

interface FormData {
  title: string;
  company: string;
  startMonth: { value: number; label: string } | null;
  startYear: { value: number; label: string } | null;
  location?: string;
  endMonth?: { value: number; label: string } | null;
  endYear?: { value: number; label: string } | null;
  description?: string;
}

// TODO: rewrite in common files
const monthOptions = [
  { label: 'January', value: 1 },
  { label: 'February', value: 2 },
  { label: 'March', value: 3 },
  { label: 'April', value: 4 },
  { label: 'May', value: 5 },
  { label: 'June', value: 6 },
  { label: 'July', value: 7 },
  { label: 'August', value: 8 },
  { label: 'September', value: 9 },
  { label: 'October', value: 10 },
  { label: 'November', value: 11 },
  { label: 'December', value: 12 },
];

const currentYear = new Date().getFullYear();
const yearOptions = Array.from({ length: 100 }, (_, i) => {
  const year = currentYear - i;
  return { label: year.toString(), value: year };
});

export default function ExperienceModal({
  open,
  isUpdating,
  initialData,
  onClose,
  onSave,
}: IProps) {
  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setError,
    setValue,
    clearErrors,
  } = useForm<FormData>({
    defaultValues: {
      title: '',
      company: '',
      startMonth: null,
      startYear: null,
      location: '',
      endMonth: null,
      endYear: null,
      description: '',
    },
  });

  const endMonth = watch('endMonth');
  const endYear = watch('endYear');

  useEffect(() => {
    if (open) {
      if (initialData) {
        reset({
          title: initialData.title || '',
          company: initialData.company || '',
          startMonth:
            initialData.startMonth === undefined
              ? undefined
              : monthOptions.find((m) => m.value === initialData.startMonth),
          startYear:
            initialData.startYear === undefined
              ? undefined
              : {
                  value: initialData.startYear,
                  label: initialData.startYear.toString(),
                },
          location: initialData.location || '',
          endMonth:
            initialData.endMonth === undefined
              ? undefined
              : monthOptions.find((m) => m.value === initialData.endMonth),
          endYear:
            initialData.endYear === undefined
              ? undefined
              : {
                  value: initialData.endYear,
                  label: initialData.endYear.toString(),
                },
          description: initialData.description || '',
        });
      } else {
        reset({
          title: '',
          company: '',
          startMonth: undefined,
          startYear: undefined,
          location: '',
          endMonth: undefined,
          endYear: undefined,
          description: '',
        });
      }
    }
  }, [open, initialData, reset]);

  // Validate endMonth and endYear dependency
  useEffect(() => {
    if (endMonth && !endYear) {
      setError('endYear', {
        type: 'required',
        message: 'End year is required when end month is selected',
      });
    } else {
      clearErrors('endYear');
    }
  }, [endMonth, endYear, setError, clearErrors]);

  const onSubmit = async (data: FormData) => {
    // Additional validation for endMonth/endYear
    if (data.endMonth && !data.endYear) {
      setError('endYear', {
        type: 'required',
        message: 'End year is required when end month is selected',
      });
      return;
    } else if (data.endYear && !data.endMonth) {
      setError('endMonth', {
        type: 'required',
        message: 'End month is required when end year is selected',
      });
      return;
    }

    if (!data.startMonth || !data.startYear) {
      setError('startYear', {
        type: 'required',
        message: 'Start month and year are required',
      });
      return;
    }

    // compare end month, year
    if (data.endMonth && data.endYear) {
      // compare with start month, year
      if (
        data.endYear.value < data.startYear.value ||
        (data.endYear.value === data.startYear.value &&
          data.endMonth.value < data.startMonth.value)
      ) {
        setError('endMonth', {
          type: 'validate',
          message: 'End date must be after start date',
        });
        return;
      }
    }

    if (!onSave) return;

    try {
      const experienceData: UserCVExperience = {
        id: initialData?.id || '',
        title: data.title,
        company: data.company,
        startMonth: data.startMonth.value,
        startYear: data.startYear.value,
        location: data.location || undefined,
        endMonth: data.endMonth?.value || undefined,
        endYear: data.endYear?.value || undefined,
        description: data.description || undefined,
      };

      const result = await onSave(experienceData);
      if (result) {
        onClose();
      }
    } catch (error) {
      console.error('Error saving experience:', error);
    }
  };

  const title = initialData ? 'Edit Work Experience' : 'Add Work Experience';

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      size="lg"
      containerClassName="md:w-[600px]"
    >
      <div className="w-full rounded-[20px] p-6">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">{title}</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Title */}
          <div>
            <FieldLabel title="Title" />
            <Controller
              name="title"
              control={control}
              rules={{ required: 'Title is required' }}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="flat"
                  placeholder="e.g. Software Engineer"
                  className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                  inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  error={errors.title?.message}
                  errorClassName="text-red-500 text-xs !bg-transparent"
                />
              )}
            />
          </div>

          {/* Company */}
          <div>
            <FieldLabel title="Company or organization" />
            <Controller
              name="company"
              control={control}
              rules={{ required: 'Company is required' }}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="flat"
                  placeholder="e.g. Google Inc."
                  className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                  inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  error={errors.company?.message}
                  errorClassName="text-red-500 text-xs !bg-transparent"
                />
              )}
            />
          </div>

          {/* Start Date */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <FieldLabel title="Start Month" />
              <Controller
                name="startMonth"
                control={control}
                rules={{ required: 'Start month is required' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={monthOptions}
                    placeholder="Select month"
                    className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                    error={errors.startMonth?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
            <div>
              <FieldLabel title="Start Year" />
              <Controller
                name="startYear"
                control={control}
                rules={{ required: 'Start year is required' }}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={yearOptions}
                    placeholder="Select year"
                    className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                    error={errors.startYear?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
          </div>

          {/* End Date */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <FieldLabel title="End Month" />
              <Controller
                name="endMonth"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={monthOptions}
                    placeholder="Select month"
                    clearable
                    onClear={(e) => {
                      e.stopPropagation();
                      setValue('endMonth', null);
                    }}
                    className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                    error={errors.endMonth?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
            <div>
              <FieldLabel title="End Year" />
              <Controller
                name="endYear"
                control={control}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={yearOptions}
                    placeholder="Select year"
                    clearable
                    onClear={(e) => {
                      e.stopPropagation();
                      setValue('endYear', null);
                    }}
                    className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                    error={errors.endYear?.message}
                    errorClassName="text-red-500 text-xs"
                  />
                )}
              />
            </div>
          </div>

          {/* Location */}
          <div>
            <FieldLabel title="Location" />
            <Controller
              name="location"
              control={control}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="flat"
                  placeholder="e.g. San Francisco, CA"
                  className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                  inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                />
              )}
            />
          </div>

          {/* Description */}
          <div className="w-full">
            <FieldLabel title="Description" />
            <Controller
              name="description"
              control={control}
              render={({ field }) => (
                <Textarea
                  {...field}
                  variant="flat"
                  placeholder="Describe your responsibilities and achievements..."
                  className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                  rows={6}
                />
              )}
            />
          </div>

          <div className="mt-6 flex justify-end gap-3">
            <Button
              type="submit"
              className="bg-primary text-white"
              disabled={isUpdating}
            >
              {isUpdating ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
