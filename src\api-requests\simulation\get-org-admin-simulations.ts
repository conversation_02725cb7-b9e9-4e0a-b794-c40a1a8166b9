import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { cleanQueryParams } from '@/utils/url';
import { useQuery } from '@tanstack/react-query';
import { ApiListResponse } from '../types';
import { AdminSimulation, SimulationQueryKeys } from './types';

export async function getListAdminSimulations(params: {
  limit: number;
  page: number;
  orgId: string;
  keyword?: string;
  status?: string;
}): Promise<ApiListResponse<AdminSimulation>> {
  const response = await requestGet<ApiListResponse<AdminSimulation>>(
    API_ENDPONTS.GET_ORG_ADMIN_SIMULATION,
    cleanQueryParams(params),
    { orgId: params.orgId }
  );
  return response.data;
}

export function useGetOrgAdminSimulations(params: {
  limit: number;
  page: number;
  orgId: string;
  keyword?: string;
  status?: string;
}) {
  return useQuery<ApiListResponse<AdminSimulation>>({
    queryKey: [SimulationQueryKeys.GET_ORG_ADMIN_SIMULATIONS, params],
    queryFn: () => getListAdminSimulations(params),
    enabled: !!params.orgId,
  });
}
