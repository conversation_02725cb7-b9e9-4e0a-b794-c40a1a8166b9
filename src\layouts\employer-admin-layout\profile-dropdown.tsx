'use client';

import { useAuthActions } from '@/hooks/use-auth-actions';
import { UserInfo } from '@/store/user-atom';
import { Avatar, Dropdown } from 'rizzui';

interface IProps {
  userInfo: UserInfo;
  dropdownTrigger?: React.ReactNode;
}

export default function ProfileDropdown({ userInfo, dropdownTrigger }: IProps) {
  const { logout } = useAuthActions();

  return (
    <Dropdown>
      <Dropdown.Trigger>
        {dropdownTrigger || (
          <Avatar
            name={`${userInfo.firstName} ${userInfo.lastName}`}
            src={userInfo.avatar || '/avatar/user-default.png'}
            size="md"
            className="h-10 w-10 cursor-pointer rounded-full !bg-transparent object-cover"
          />
        )}
      </Dropdown.Trigger>
      <Dropdown.Menu className="w-fit divide-y">
        <Dropdown.Item
          className="hover:bg-primary hover:text-white"
          onClick={() => logout()}
        >
          Logout
        </Dropdown.Item>
      </Dropdown.Menu>
    </Dropdown>
  );
}
