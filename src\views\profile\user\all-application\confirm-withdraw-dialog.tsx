'use client';

import { userAtom } from '@/store/user-atom';
import { useAtom } from 'jotai';
import { Button, Modal } from 'rizzui';

interface IProps {
  open: boolean;
  isLoading: boolean;
  setOpen: (open: boolean) => void;
  onConfirm: () => void;
}

export default function ConfirmWithdrawApplicationDialog({
  open,
  isLoading,
  setOpen,
  onConfirm,
}: IProps) {
  return (
    <Modal isOpen={open} onClose={() => setOpen(false)}>
      <div className="flex flex-col items-center gap-5 p-6 text-center">
        <p className="text-xl font-bold text-[#484848]">
          Are you sure you want to withdraw your application?
        </p>
        <p className="text-[16px]">
          This action cannot be undone. Please confirm your decision.
        </p>
        <div className="flex flex-row gap-4">
          <Button
            onClick={() => setOpen(false)}
            variant="outline"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            variant="solid"
            className="text-white"
            disabled={isLoading}
          >
            Confirm
          </Button>
        </div>
      </div>
    </Modal>
  );
}
