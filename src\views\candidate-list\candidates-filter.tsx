'use client';

import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import { SelectOption } from '@/api-requests/types';
import SearchIcon from '@/views/icons/search';
import { useState } from 'react';
import {
  ActionIcon,
  Button,
  Dropdown,
  Input,
  InputProps,
  Select,
  Tooltip
} from 'rizzui';
import HeartOutlineIcon from '../icons/heart-outline';
import JobAutoComplete from './job-autocomplete';

const statusOptions: SelectOption[] = Object.values(ApplicationStatus).map(
  (status) => ({
    label: status.charAt(0).toUpperCase() + status.slice(1),
    value: status,
  })
);

const applyMethodOptions: SelectOption[] = [
  { label: 'CV', value: 'cv' },
  { label: 'Simulation', value: 'simulation' },
];

const sortOptions: SelectOption[] = [
  { label: 'Applied: Newest', value: 'appliedAt:desc' },
  { label: 'Applied: Oldest', value: 'appliedAt:asc' },
  { label: 'Match: Highest', value: 'matchPercentage:desc' },
  { label: 'Match: Lowest', value: 'matchPercentage:asc' },
];

const rowsPerPageOptions: SelectOption[] = [
  { label: '10', value: '10' },
  { label: '20', value: '20' },
  { label: '50', value: '50' },
];

interface IProps {
  searchProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  selectedCandidates: ShortlistCandidate[];
  totalCandidates: number;
  sortDefaultValue: string | null;
  onHeartClick: () => void;
  onSelectJob: (jobId: string | null) => void;
  onSelectStatus: (value: string | null) => void;
  onSelectApplyMethod: (value: string | null) => void;
  onSelectSort: (value: string | null) => void;
}

export default function CandidatesFilter({
  searchProps,
  selectedCandidates,
  totalCandidates,
  sortDefaultValue,
  onHeartClick,
  onSelectJob,
  onSelectStatus,
  onSelectApplyMethod,
  onSelectSort,
}: IProps) {
  const [selectedStatus, setSelectedStatus] = useState<SelectOption | null>(
    null
  );
  const [selectedApplyMethod, setSelectedApplyMethod] =
    useState<SelectOption | null>(null);
  const [selectedSort, setSelectedSort] = useState<SelectOption | null>(
    sortOptions.find((option) => option.value === sortDefaultValue) || null
  );

  const handleSelectStatus = (option: SelectOption | null) => {
    setSelectedStatus(option);
    onSelectStatus(option?.value || null);
  };
  const handleSelectApplyMethod = (option: SelectOption | null) => {
    setSelectedApplyMethod(option);
    onSelectApplyMethod(option?.value || null);
  };
  const handleSelectSort = (option: SelectOption | null) => {
    setSelectedSort(option);
    onSelectSort(option?.value || null);
  };

  return (
    <div className="flex flex-col gap-3">
      <div className="flex w-full flex-nowrap items-center gap-3 sm:gap-4">
        <div className="flex flex-row gap-2">
          <Input
            prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
            className="min-w-[250px]"
            placeholder="Search candidate name, email"
            size="md"
            {...searchProps}
          />
        </div>

        <div className="flex items-center gap-2 whitespace-nowrap text-sm text-gray-500">
          <div className="text-gray-400">{totalCandidates} Results</div>
          {selectedCandidates.length > 0 && (
            <>
              <div className="h-4 w-px bg-gray-300" />
              <div className="font-medium text-gray-900">
                {selectedCandidates.length} selected
              </div>
              <div className="h-4 w-px bg-gray-300" />
              <Tooltip color="invert" content="Add to shortlist">
                <ActionIcon
                  variant="text"
                  size="sm"
                  className="h-fit w-fit"
                  onClick={onHeartClick}
                >
                  <HeartOutlineIcon className="h-5 w-5 text-gray-500 hover:text-red-500" />
                </ActionIcon>
              </Tooltip>
            </>
          )}
        </div>
      </div>

      <div className="flex gap-12">
        {/* Filter container */}
        <div className="flex flex-col gap-1">
          <div className="whitespace-nowrap text-sm opacity-50">Filter by:</div>

          <div className="flex w-full flex-row flex-wrap gap-3">
            <div className="w-60">
              <JobAutoComplete onSelectJob={onSelectJob} />
            </div>
            <Select
              clearable
              options={statusOptions}
              placeholder="Select status"
              className="w-full min-w-[140px] rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md [&_.rizzui-select-button]:!border [&_.rizzui-select-button]:!border-gray-200"
              size="md"
              value={selectedStatus}
              onChange={handleSelectStatus}
              onClear={() => handleSelectStatus(null)}
            />
            <Select
              clearable
              options={applyMethodOptions}
              placeholder="Apply method"
              className="w-full min-w-[120px] rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md [&_.rizzui-select-button]:!border [&_.rizzui-select-button]:!border-gray-200"
              size="md"
              value={selectedApplyMethod}
              onChange={handleSelectApplyMethod}
              onClear={() => handleSelectApplyMethod(null)}
            />
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <div className="whitespace-nowrap text-sm opacity-50">Sort by:</div>

          <div className="flex w-full flex-row flex-wrap gap-3">
            <Select
              clearable
              options={sortOptions}
              placeholder="Sort by applied date, match"
              className="w-full min-w-[200px] rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md [&_.rizzui-select-button]:!border [&_.rizzui-select-button]:!border-gray-200"
              size="md"
              value={selectedSort}
              onChange={handleSelectSort}
              onClear={(e) => {
                e.stopPropagation();
                e.preventDefault();
                if (selectedSort?.value === 'appliedAt:desc') return;

                handleSelectSort(
                  sortOptions.find(
                    (option) => option.value === 'appliedAt:desc'
                  ) || null
                );
              }}
            />
          </div>
        </div>
        <div className="ml-auto flex min-w-20 flex-col items-center justify-center gap-1">
          <div className="whitespace-nowrap text-sm opacity-50">
            Bulk Action
          </div>

          <div className="flex w-full flex-row flex-wrap gap-3">
            <Dropdown>
              <Dropdown.Trigger disabled={!selectedCandidates.length}>
                <Button
                  as="span"
                  variant="outline"
                  disabled={!selectedCandidates.length}
                >
                  Action {`(${selectedCandidates.length})`}
                </Button>
              </Dropdown.Trigger>
              <Dropdown.Menu>
                <Dropdown.Item>Add to Shortlist</Dropdown.Item>
                <Dropdown.Item>Compare</Dropdown.Item>
                <Dropdown.Item>Export Data</Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </div>
        </div>
      </div>
    </div>
  );
}
