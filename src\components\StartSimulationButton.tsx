'use client';

import { useApplySimulation } from '@/api-requests/job/apply-simulation';
import { Role } from '@/api-requests/user/types';
import { userAtom } from '@/store/user-atom';
import { CAREER_AT_TOKEN, getClientSideCookie } from '@/utils/http-client';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import { useAtom } from 'jotai';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { Button } from 'rizzui';
import { createPortal } from 'react-dom';
import { getUserError } from '@/utils/api-error';

interface IProps {
  jobId?: string;
  simId: string;
  buttonProps?: React.ComponentProps<typeof Button>;
  children?: React.ReactNode;
  onErrorJobApplied?: () => void;
}

export default function StartSimulationButton({
  jobId,
  simId,
  buttonProps,
  children,
  onErrorJobApplied,
}: IProps) {
  const [openLoginModal, setOpenLoginModal] = useState(false);
  const [user] = useAtom(userAtom);
  const { mutateAsync: applySimulation } = useApplySimulation({
    onError: (error: any) => {
      const userError = getUserError(
        error,
        'Failed to apply for this job. Please try again later.'
      );
      toast[userError.code === 'JOB_APPLIED' ? 'success' : 'error'](
        userError.message
      );
      if (userError.code === 'JOB_APPLIED') {
        onErrorJobApplied?.();
      }
    },
  });

  const goToSimLink = async () => {
    if (!simId) return '';

    const link = await applySimulation({
      jobId,
      simulationId: simId,
    });

    if (link) {
      window.open(link, '_blank');
    } else {
      toast.error('Failed to apply. Please try again later.');
    }
  };

  const checkUserLogin = () => {
    const accessToken = getClientSideCookie(CAREER_AT_TOKEN);
    if (!accessToken || !user) {
      setOpenLoginModal(true);
      return false;
    }
    if (user?.role !== Role.USER) {
      toast.error('Only users can apply for this job.');
      return false;
    }

    return true;
  };

  const handleStartSimulation = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    e.stopPropagation();
    if (!checkUserLogin()) return;

    goToSimLink();
  };

  const handleLoginSuccess = () => {
    goToSimLink();
  };

  return (
    <>
      <Button onClick={handleStartSimulation} {...buttonProps}>
        {children || 'Start Simulation'}
      </Button>
      {openLoginModal &&
        createPortal(
          <SignInModal
            open={openLoginModal}
            onClose={() => setOpenLoginModal(false)}
            role={Role.USER}
            onLoginSuccess={handleLoginSuccess}
          />,
          document.body
        )}
    </>
  );
}
