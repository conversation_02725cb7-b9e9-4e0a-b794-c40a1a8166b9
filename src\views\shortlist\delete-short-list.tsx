import { But<PERSON> } from 'rizzu<PERSON>';
import { Shortlist } from '@/api-requests/shortlist';

interface IProps {
  isLoading?: boolean;
  onDelete?: (shortlist: Shortlist) => void;
  shortlist: Shortlist;
}

export default function DeleteShortlist({
  isLoading,
  onDelete,
  shortlist,
}: IProps) {
  return (
    <div className="space-y-4">
      <div>
        Are you sure you want to remove the shortlist <b>{shortlist?.name}</b>?
      </div>

      <div className="flex justify-end gap-3">
        <Button variant="outline" className="border-primary text-primary">
          Cancel
        </Button>
        <Button
          className="bg-primary text-white"
          isLoading={isLoading}
          disabled={isLoading}
          onClick={() => onDelete?.(shortlist)}
        >
          Save
        </Button>
      </div>
    </div>
  );
}
