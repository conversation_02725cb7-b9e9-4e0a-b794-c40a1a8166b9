import React from 'react';

function TechnologyIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_3183_32604)">
        <path
          d="M6.41756 10.5H5.71656C3.92406 10.5 3.02806 10.5 2.47106 9.9325C1.91406 9.365 1.91406 8.4515 1.91406 6.625C1.91406 4.7985 1.91406 3.885 2.47106 3.3175C3.02806 2.75 3.92406 2.75 5.71656 2.75H7.61806C9.41056 2.75 10.3071 2.75 10.8641 3.3175C11.2926 3.754 11.3911 4.3955 11.4141 5.5"
          stroke="#484848"
          strokeWidth="0.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M8.91406 2.75L8.86406 2.595C8.61656 1.825 8.49306 1.44 8.19856 1.22C7.90356 1 7.51256 1 6.72906 1H6.59756C5.81506 1 5.42356 1 5.12906 1.22C4.83406 1.44 4.71056 1.825 4.46306 2.595L4.41406 2.75M9.46956 6.6275C9.56206 6.5425 9.60806 6.5 9.66406 6.5C9.72006 6.5 9.76606 6.5425 9.85856 6.6275L10.2151 6.956C10.2581 6.9955 10.2796 7.0155 10.3061 7.025C10.3331 7.035 10.3621 7.034 10.4211 7.0315L10.9021 7.0125C11.0261 7.0075 11.0881 7.0055 11.1306 7.041C11.1731 7.0765 11.1816 7.138 11.1981 7.261L11.2641 7.754C11.2721 7.811 11.2756 7.839 11.2896 7.864C11.3036 7.888 11.3261 7.9055 11.3711 7.941L11.7591 8.246C11.8551 8.322 11.9031 8.3595 11.9126 8.4135C11.9221 8.4675 11.8896 8.5195 11.8256 8.6235L11.5626 9.047C11.5326 9.0955 11.5176 9.12 11.5126 9.147C11.5076 9.174 11.5136 9.2025 11.5256 9.2585L11.6301 9.7475C11.6551 9.8675 11.6681 9.9275 11.6406 9.9755C11.6131 10.0235 11.5546 10.0425 11.4381 10.0805L10.9751 10.231C10.9201 10.249 10.8921 10.258 10.8706 10.276C10.8491 10.2945 10.8356 10.3205 10.8091 10.372L10.5831 10.8075C10.5256 10.919 10.4966 10.9745 10.4441 10.9935C10.3916 11.0125 10.3341 10.9885 10.2181 10.9405L9.77406 10.7565C9.71956 10.734 9.69256 10.7225 9.66406 10.7225C9.63556 10.7225 9.60856 10.734 9.55406 10.7565L9.11006 10.9405C8.99406 10.9885 8.93656 11.0125 8.88406 10.9935C8.83156 10.9745 8.80256 10.9185 8.74506 10.8075L8.51906 10.372C8.49206 10.3205 8.47906 10.2945 8.45756 10.2765C8.43606 10.2585 8.40806 10.249 8.35306 10.2315L7.89006 10.0805C7.77356 10.0425 7.71506 10.0235 7.68756 9.9755C7.66006 9.9275 7.67256 9.868 7.69806 9.7475L7.80306 9.2585C7.81456 9.2025 7.82056 9.1745 7.81556 9.1475C7.80531 9.11133 7.78835 9.07741 7.76556 9.0475L7.50306 8.6235C7.43806 8.5195 7.40606 8.4675 7.41556 8.4135C7.42506 8.3595 7.47306 8.322 7.56906 8.2465L7.95706 7.9415C8.00206 7.9055 8.02456 7.888 8.03856 7.8635C8.05256 7.839 8.05606 7.811 8.06356 7.7535L8.13006 7.261C8.14656 7.1385 8.15506 7.0765 8.19756 7.041C8.24006 7.0055 8.30206 7.0075 8.42606 7.0125L8.90756 7.0315C8.96606 7.034 8.99506 7.035 9.02206 7.025C9.04856 7.015 9.07006 6.9955 9.11306 6.956L9.46956 6.6275Z"
          stroke="#484848"
          strokeWidth="0.8"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_3183_32604">
          <rect
            width="12"
            height="12"
            fill="white"
            transform="translate(0.914062)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default TechnologyIcon;
