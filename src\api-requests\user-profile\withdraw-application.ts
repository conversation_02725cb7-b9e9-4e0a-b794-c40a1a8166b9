import { API_ENDPONTS } from '@/config/endpoint';
import { requestPost } from '@/utils/http-client';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export async function withdrawApplication(params: {
  id: string;
}): Promise<boolean> {
  const response = await requestPost<{ success: boolean }>(
    API_ENDPONTS.WITHDRAW_APPLICATION,
    {},
    params
  );
  return response.data.success;
}

export const useWithdrawApplication = (
  options?: UseMutationOptions<any, AxiosError, { id: string }>
) => {
  return useMutation({
    mutationFn: (params: { id: string }) => withdrawApplication(params),
    ...options,
  });
};
