'use client';

import { useGetCandidateByOrg } from '@/api-requests/job-candidate/get-candidate-by-org';
import { orgAtom } from '@/store/organization-atom';
import { safeFormatDate } from '@/utils/date';
import { useAtom } from 'jotai';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { ActionIcon, Avatar, Button } from 'rizzui';
import ArrowLeftIcon from '../icons/arrow-left';
import HeartOutlineIcon from '../icons/heart-outline';
import CadidateTabs from './candidate-tabs';

export default function CandidateDetail(props: any) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentView = searchParams.get('view');
  const { id: candidateId } = useParams<{ id: string }>();
  const [org] = useAtom(orgAtom);
  const { data: candidate } = useGetCandidateByOrg(org?._id || '', candidateId);

  const handleBack = () => {
    let route = '/org/admin/candidates';
    if (currentView === 'shortlist') {
      route = '/org/admin/candidates?view=shortlist';
    }
    router.push(route);
  };

  if (!candidate) return null;

  return (
    <div className="space-y-6">
      <Button
        variant="flat"
        className="flex items-center gap-2 pb-4"
        onClick={handleBack}
      >
        <ArrowLeftIcon className="h-6 w-6" />
        <span>Candidate details</span>
      </Button>

      <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-start">
        <div className="flex flex-col items-start gap-6 sm:flex-row sm:items-center">
          <div className="relative shrink-0">
            <div className="rounded-full ring-4 ring-[#3E5C76]">
              <Avatar
                // src={'/avatar/user-default.png'}
                src={candidate.user?.avatar || '/avatar/user-default.png'}
                name={candidate.user?.firstName || ''}
                customSize={116}
              />
            </div>
          </div>

          <div className="flex-1">
            <h1 className="text-lg font-extrabold tracking-tight text-slate-900 sm:text-xl">
              {candidate.user?.firstName} {candidate.user?.lastName}
            </h1>

            <div className="mt-2 grid grid-cols-[120px_1fr] gap-y-1">
              <div className="text-sm font-medium text-gray-500">
                Simulation:
              </div>
              <div>
                {/* <Link
                  href="/org/admin/candidates"
                  className="underline-offset-[3px underline decoration-2" //hover:decoration-sky-500
                >
                  {candidate.simulation?.name}
                </Link> */}
                {candidate.simulation?.name}
              </div>

              <div className="text-sm font-medium text-gray-500">Task:</div>
              <div>
                {candidate.simulation?.tasks
                  ? candidate.simulation.tasks.length
                  : 'N/A'}{' '}
                Task(s)
              </div>

              <div className="text-sm font-medium text-gray-500">Date:</div>
              <div>
                {safeFormatDate(
                  new Date(candidate.completedAt || candidate.appliedAt),
                  {
                    format: 'full',
                  }
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <ActionIcon variant="outline">
            <HeartOutlineIcon className="h-6 w-6" />
          </ActionIcon>
          <Button variant="outline" className="border-primary text-primary">
            Contact Candidate
          </Button>
        </div>
      </div>

      <CadidateTabs candidate={candidate} />
    </div>
  );
}
