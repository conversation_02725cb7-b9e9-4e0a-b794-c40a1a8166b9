const getUserError = (error: any, defaultMessage?: string) => {
  const [errorType, errorCode] = (error?.response?.data?.code || '').split(':');
  if (errorType === 'USER_MESSAGE') {
    return {
      message: error?.response?.data?.message || defaultMessage || '',
      code: errorCode || '',
    };
  }
  return {
    message: defaultMessage || 'An unexpected error occurred',
  };
};

export { getUserError };
