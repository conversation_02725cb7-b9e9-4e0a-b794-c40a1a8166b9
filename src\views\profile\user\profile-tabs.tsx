'use client';

import { UserProfile } from '@/api-requests/user-profile';
import cn from '@/utils/class-names';
import LocationIcon from '@/views/icons/location';
import Image from 'next/image';
import { Avatar } from 'rizzui';

export interface ProfileTabsProps {
  setActiveTab: (tab: string) => void;
  activeTab: string;
  tabs: { name: string; hash: string }[];
  profile: UserProfile | null;
}

export default function ProfileTabs({
  setActiveTab,
  activeTab,
  tabs,
  profile,
}: ProfileTabsProps) {
  return (
    <div className="relative flex w-full flex-col items-center rounded-[20px] p-6 text-center shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out lg:w-[282px]">
      <div className="absolute -top-8 left-1/2 -translate-x-1/2">
        <Image
          src="/job/profile-identification.png"
          alt="location icon"
          width={48}
          height={72}
          className="h-auto w-12"
          loader={({ src }) => src}
        />
      </div>

      <div className="mt-4">
        <Avatar
          src={profile?.avatar || '/avatar/user-default.png'}
          name={`${profile?.firstName} ${profile?.lastName}`}
          className="!h-25 !w-25 rounded-full !bg-transparent object-cover"
          customSize={100}
        />
      </div>
      <h3 className="mt-4 text-lg font-semibold text-gray-900">
        {profile?.firstName} {profile?.lastName}
      </h3>
      <div className="flex items-center justify-center gap-2">
        <LocationIcon className="h-4 w-4 text-gray-500" />
        <span className="text-sm text-gray-500">
          {profile?.country ? profile.country : '-'}
        </span>
      </div>

      <div className="mt-8 flex flex-col gap-4">
        {tabs.map((tab) => (
          <button
            key={tab.hash}
            onClick={() => setActiveTab(tab.hash)}
            className={cn(
              'font-medium transition-all duration-200',
              activeTab === tab.hash
                ? 'relative text-primary after:mt-1 after:block after:h-[2px] after:w-full after:bg-primary'
                : 'text-gray-400 hover:text-gray-600'
            )}
          >
            {tab.name}
          </button>
        ))}
      </div>
    </div>
  );
}
