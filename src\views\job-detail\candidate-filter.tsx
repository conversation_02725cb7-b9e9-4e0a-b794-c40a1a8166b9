'use client';

import { Input, InputProps, SelectProps, Select } from 'rizzui';
import SearchIcon from '@/views/icons/search';
import { SelectOption } from '@/api-requests/types';

const statusOptions: SelectOption[] = [
  { label: 'Active', value: 'active' },
  { label: 'Completed', value: 'completed' },
];

interface IProps {
  searchProps?: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  statusProps?: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
}

export default function CandidateFilter({ searchProps, statusProps }: IProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="w-full sm:w-auto">
        <Input
          variant="flat"
          prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Search candidate name"
          className="w-full min-w-[200px] rounded-lg px-2 shadow-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
          inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
          size="sm"
          {...searchProps}
        />
      </div>

      <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
        {/* Search bar - full width on mobile, constrained on larger screens */}
        {/* <div className="w-full sm:w-auto">
          <Input
            variant="flat"
            prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
            placeholder="Search candidate name"
            className="w-full min-w-[200px] rounded-lg px-2 shadow-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
            inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
            size="sm"
            {...searchProps}
          />
        </div> */}

        {/* Filter container */}
        <div className="flex flex-wrap items-center gap-3">
          <div className="whitespace-nowrap text-sm opacity-50">Filter by:</div>

          <div className="flex flex-wrap gap-3">
            <Select
              clearable
              options={statusOptions}
              placeholder="Select status"
              className="w-full min-w-[120px] rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md [&_.rizzui-select-button]:!border [&_.rizzui-select-button]:!border-gray-200"
              size="sm"
              {...statusProps}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
