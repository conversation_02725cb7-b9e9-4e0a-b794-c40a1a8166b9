'use client';

import { Tab } from 'rizzui';
import OverviewTab from './overview-tab';
import ProfileTab from './profile-tab';
import { JobCandidate } from '@/api-requests/job-candidate/types';

interface IProps {
  candidate: JobCandidate;
}

export default function CadidateTabs({ candidate }: IProps) {
  return (
    <Tab className={'[&_.rizzui-tab-list]:border-none'}>
      <Tab.List>
        <Tab.ListItem>Overview</Tab.ListItem>
        <Tab.ListItem>Profile</Tab.ListItem>
      </Tab.List>
      <Tab.Panels>
        <Tab.Panel>
          <OverviewTab candidate={candidate} />
        </Tab.Panel>
        <Tab.Panel>
          <ProfileTab candidate={candidate} />
        </Tab.Panel>
      </Tab.Panels>
    </Tab>
  );
}
