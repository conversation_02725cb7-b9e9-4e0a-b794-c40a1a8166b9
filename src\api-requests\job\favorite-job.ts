import axiosInstance from '@/utils/http-client';
import { Job, JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Interaction } from '../interaction';

export async function favoriteJob(jobId: string): Promise<boolean> {
  const response = await axiosInstance.post<boolean>(
    API_ENDPONTS.FAVORITE_JOB.replace(':jobId', jobId)
  );
  return response.data;
}

export const useFavoriteJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobId: string) => favoriteJob(jobId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.FAVORITE_JOB],
      });
    },
  });
};
