'use client';

import Image from 'next/image';
import EditIcon from '@/views/icons/edit';
import PlusSquareIcon from '@/views/icons/plus-square';
import DeleteIcon from '@/views/icons/delete';
import CaseIcon from '@/views/icons/case';
import EyeIcon from '@/views/icons/eye';
import CalendarIcon from '@/views/icons/calendar';
import ArrowUpRightIcon from '@/views/icons/arrow-up-right';

export default function JobCard() {
  return (
    <div className="flex w-full flex-col gap-6 md:flex-row">
      <div className="flex-1 rounded-xl p-4 shadow-md transition-shadow duration-300 hover:shadow-lg hover:shadow-gray-300/70 xl:p-6">
        <div className="flex items-start justify-between">
          <h2 className="text-xl font-semibold text-black">
            Software Development Manager
          </h2>
          <div className="flex gap-3">
            <IconButton>
              <EditIcon className="h-5 w-5 opacity-70" />
            </IconButton>
            <IconButton>
              <PlusSquareIcon className="h-5 w-5 opacity-70" />
            </IconButton>
            <IconButton>
              <DeleteIcon className="h-5 w-5" />
            </IconButton>
          </div>
        </div>

        <div className="mt-1 flex flex-wrap items-center gap-3 text-sm text-gray-500">
          <Image
            src="/job/adobe.png"
            alt="Adobe Logo"
            width={32}
            height={32}
            className="h-8 w-8 object-contain"
            loader={({ src }) => src}
          />
          <span>Adobe Inc.</span>
          <span className="flex items-center gap-1">
            <CaseIcon className="h-5 w-5 opacity-70" />
            Onsite
          </span>
          <span className="flex items-center gap-1">
            <CalendarIcon className="h-5 w-5 opacity-70" />
            Jun 24, 2025
          </span>
          <span className="flex items-center gap-1">
            <EyeIcon className="h-5 w-5 opacity-70" />
            1.151
          </span>
        </div>
      </div>

      <div className="flex w-full flex-col items-center gap-6 rounded-xl p-6 shadow-md transition-shadow duration-300 hover:shadow-lg hover:shadow-gray-300/70 md:w-auto md:flex-row xl:p-6">
        <Stat label="Candidates" value="36" />
        <Stat label="Candidate matches" value="12" />
        <Stat label="Avg Match" value="86%" />
        <div className="text-center">
          <div className="text-sm text-gray-500">Simulation</div>
          <div className="mt-1 flex w-[120px] items-center justify-between rounded-full bg-gray-100 px-3 py-1 text-sm">
            <span className="truncate">Software...</span>
            <ArrowUpRightIcon className="h-5 w-5 opacity-70" />
          </div>
        </div>
      </div>
    </div>
  );
}

export function Stat({ label, value }: { label: string; value: string | number }) {
  return (
    <div className="text-center">
      <div className="text-sm text-gray-500">{label}</div>
      <div className="text-lg font-semibold">{value}</div>
    </div>
  );
}

function IconButton({
  children,
  danger = false,
}: {
  children: React.ReactNode;
  danger?: boolean;
}) {
  return (
    <button
      className={`rounded-md p-1 ${
        danger
          ? 'bg-red-50 text-red-500 hover:bg-red-200'
          : 'bg-red-50 text-gray-700 hover:bg-gray-200'
      }`}
    >
      {children}
    </button>
  );
}
