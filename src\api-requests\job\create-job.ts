import axiosInstance from '@/utils/http-client';
import { Job, JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function createJob(params: Job): Promise<Job> {
  const response = await axiosInstance.post<Job>(
    API_ENDPONTS.JOBS_LIST,
    params
  );
  return response.data;
}

export const useCreateJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: Job) => createJob(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.CREATE_JOB],
      });
    },
  });
};
