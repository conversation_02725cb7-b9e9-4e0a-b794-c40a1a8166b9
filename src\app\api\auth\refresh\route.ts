import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { AT_COOKIE, RT_COOKIE, isProd, maxAgeDays } from '../_utils';
import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';

export async function POST() {
  const jar = await cookies();
  const rt = jar.get(RT_COOKIE)?.value;
  if (!rt) {
    jar.delete(RT_COOKIE);
    jar.delete(AT_COOKIE);
    return NextResponse.json(
      {
        ok: false,
        message: 'Missing refresh token',
        // TODO: For now: Do not redirect if refresh failed. Fixed API refresh first, then handle redirect.
        shouldRedirect: false,
      },
      { status: 500 }
    );
  }

  try {
    const { data } = await axiosInstance.post(
      API_ENDPONTS.REFRESH_TOKEN,
      {},
      { headers: { Authorization: `Bearer ${rt}` } }
    );
    const { accessToken, refreshToken, user } = data || {};
    const res = NextResponse.json({ user });

    if (accessToken) {
      res.cookies.set(AT_COOKIE, accessToken, {
        httpOnly: false,
        sameSite: 'lax',
        secure: isProd,
        path: '/',
        maxAge: maxAgeDays(1),
      });
    }
    if (refreshToken) {
      res.cookies.set(RT_COOKIE, refreshToken, {
        httpOnly: true,
        sameSite: 'lax',
        secure: isProd,
        path: '/',
        maxAge: maxAgeDays(7),
      });
    }

    return res;
  } catch {
    const res = NextResponse.json(
      { ok: false, message: 'Refresh failed' },
      { status: 500 }
    );
    res.cookies.delete(AT_COOKIE);
    res.cookies.delete(RT_COOKIE);
    return res;
  }
}
