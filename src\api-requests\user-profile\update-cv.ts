import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { UserProfile, UserProfileQueryKeys } from './types';
import { userAtom } from '@/store/user-atom';
import { useSetAtom } from 'jotai';
import { Role } from '../user/types';

// TODO: For now, we put CV inside user profile. Will refactor later
export async function updateCV(params: {
  id?: string;
  type: string;
  data: any;
}): Promise<UserProfile> {
  const response = await axiosInstance.patch<UserProfile>(
    API_ENDPONTS.UPDATE_CV,
    params
  );
  return response.data;
}

export const useUpdateCV = () => {
  const setUser = useSetAtom(userAtom);

  return useMutation({
    mutationFn: (params: { id?: string; type: string; data: any }) =>
      updateCV(params),
    onSuccess: (res) => {
      setUser((prev) =>
        prev
          ? { ...prev, profile: res }
          : {
              profile: res,
              firstName: '',
              lastName: '',
              userId: '',
              email: '',
              id: '',
              avatar: '',
              role: Role.USER,
            }
      );
    },
  });
};
