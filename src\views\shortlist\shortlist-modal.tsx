import { JobCandidate } from '@/api-requests/job-candidate/types';
import { Shortlist } from '@/api-requests/shortlist';
import { useCallback, useState } from 'react';
import { ActionIcon, Modal, Title } from 'rizzui';
import ArrowLeftIcon from '../icons/arrow-left';
import CloseIcon from '../icons/close';
import DeleteShortlist from './delete-short-list';
import EditShortlist from './edit-short-list';
import ShortlistContent from './shortlist-content';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';

interface IProps {
  open: boolean;
  onClose: () => void;
  shortlists: Shortlist[];
  canUpdate?: boolean;
  onCheckboxChange: (
    checked: boolean,
    list: Shortlist,
    candidate: ShortlistCandidate | ShortlistCandidate[] 
  ) => void;
  candidate: ShortlistCandidate | null;
  candidates?: ShortlistCandidate[];
  isLoading?: boolean;
  onCreate: () => void;
  onEdit?: (name: string, s: Shortlist) => Promise<boolean>;
  onDelete?: (s: Shortlist) => Promise<boolean>;
}

export default function ShortlistModal({
  open,
  onClose,
  shortlists,
  canUpdate,
  onCheckboxChange,
  candidate,
  candidates,
  isLoading,
  onCreate,
  onEdit,
  onDelete,
}: IProps) {
  const [shortlist, setShortlist] = useState<Shortlist | null>(null);
  const [view, setView] = useState<'list' | 'edit' | 'delete'>('list');

  const renderTitle = useCallback(() => {
    switch (view) {
      case 'list':
        return <Title as="h4">Add to Shortlist</Title>;
      case 'edit':
        return (
          <button
            className="flex items-center gap-3"
            onClick={() => setView('list')}
          >
            <ArrowLeftIcon className="h-6 w-6" />
            <Title as="h4">Edit Shortlist</Title>
          </button>
        );
      case 'delete':
        return (
          <button
            className="flex items-center gap-3"
            onClick={() => setView('list')}
          >
            <ArrowLeftIcon className="h-6 w-6" />
            <Title as="h4">Delete Shortlist</Title>
          </button>
        );
      default:
        return '';
    }
  }, [view]);

  const renderContent = useCallback(() => {
    switch (view) {
      case 'list':
        return (
          <ShortlistContent
            shortlists={shortlists}
            candidate={candidate}
            candidates={candidates}
            isLoading={isLoading}
            canUpdate={canUpdate}
            onCreate={onCreate}
            onEdit={(s) => {
              setView('edit');
              setShortlist(s);
            }}
            onDelete={(s) => {
              setView('delete');
              setShortlist(s);
            }}
            onCheckboxChange={onCheckboxChange}
          />
        );
      case 'edit':
        return (
          <EditShortlist
            isLoading={isLoading}
            onEdit={async (name: string, s: Shortlist) => {
              const isSucess = await onEdit?.(name, s);
              if (isSucess) {
                setView('list');
              }
            }}
            shortlist={shortlist as Shortlist}
          />
        );
      case 'delete':
        return (
          <DeleteShortlist
            isLoading={isLoading}
            onDelete={async (s: Shortlist) => {
              const isSucess = await onDelete?.(s);
              if (isSucess) {
                setView('list');
              }
            }}
            shortlist={shortlist as Shortlist}
          />
        );
      default:
        return null;
    }
  }, [
    view,
    shortlists,
    candidate,
    isLoading,
    canUpdate,
    onCreate,
    onEdit,
    onCheckboxChange,
    shortlist,
  ]);

  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div className="w-full p-6 sm:w-[450px]">
        <div className="mb-7 flex items-center justify-between">
          {renderTitle()}
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        {renderContent()}
      </div>
    </Modal>
  );
}
