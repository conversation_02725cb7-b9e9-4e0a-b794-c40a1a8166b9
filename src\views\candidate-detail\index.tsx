'use client';

import { useGetCandidateByOrg } from '@/api-requests/job-candidate/get-candidate-by-org';
import { ApplicationStatus } from '@/api-requests/job-candidate/types';
import { SelectOption } from '@/api-requests/types';
import { orgAtom } from '@/store/organization-atom';
import { safeFormatDate } from '@/utils/date';
import { useAtom } from 'jotai';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { Avatar, Badge, Button, Dropdown, Select, Tooltip } from 'rizzui';
import ArrowLeftIcon from '../icons/arrow-left';
import MoreHorizontalIcon from '../icons/more-horizontal';
import CadidateTabs from './candidate-tabs';

const statusOptions = [
  { label: 'Pending', value: ApplicationStatus.PENDING },
  { label: 'Screening', value: ApplicationStatus.SCREENING },
  { label: 'Interview', value: ApplicationStatus.INTERVIEW },
  { label: 'Offer', value: ApplicationStatus.OFFER },
  { label: 'Rejected', value: ApplicationStatus.REJECTED },
  { label: 'Withdrawn', value: ApplicationStatus.WITHDRAWN },
  { label: 'Hired', value: ApplicationStatus.HIRED },
];

export default function CandidateDetail() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const currentView = searchParams.get('view');
  const { id: candidateId } = useParams<{ id: string }>();
  const [org] = useAtom(orgAtom);
  const { data: candidate } = useGetCandidateByOrg(org?._id || '', candidateId);
  const [currentStatus, setCurrentStatus] = useState<SelectOption | null>(
    statusOptions.find(
      (option) => option.value === candidate?.applicationStatus
    ) || null
  );

  const handleBack = () => {
    let route = '/org/admin/candidates';
    if (currentView === 'shortlist') {
      route = '/org/admin/candidates?view=shortlist';
    }
    router.push(route);
  };

  const handleStatusChange = (option: SelectOption) => {
    setCurrentStatus(option);
    // TODO: Call API to update status
    console.log('Update status to:', option);
  };

  const handleExportPDF = () => {};
  const handleAddToShortList = () => {};

  const handleContact = () => {};

  const handleShare = () => {};

  if (!candidate) return null;

  const matchPercentage = candidate.matchPercentage || 0;

  return (
    <div className="space-y-6">
      {/* Back Button */}
      <Button
        variant="flat"
        className="flex items-center gap-2 pb-4"
        onClick={handleBack}
      >
        <ArrowLeftIcon className="h-6 w-6" />
        <span>Candidate details</span>
      </Button>

      {/* Header Section */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          {/* Left Section - Candidate Info */}
          <div className="flex flex-col gap-6 sm:flex-row sm:items-center">
            {/* Avatar */}
            <div className="relative shrink-0">
              <Avatar
                src={candidate.user?.avatar || '/avatar/user-default.png'}
                name={candidate.user?.firstName || ''}
                customSize={100}
                className="!bg-transparent"
              />
            </div>

            {/* Candidate Details */}
            <div className="flex-1 space-y-3">
              <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-4">
                <h1 className="text-2xl font-bold text-gray-900">
                  {candidate.user?.firstName} {candidate.user?.lastName}
                </h1>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" size="sm">
                    {matchPercentage}% match
                  </Badge>
                  <Badge variant="outline" size="sm">
                    {candidate.applyMode === 'cv' ? 'CV' : 'Simulation'}
                  </Badge>
                </div>
              </div>

              {/* Job and Application Info */}
              <div className="space-y-1 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="font-medium">
                    {candidate.job?.title || candidate.simulation?.name || '-'}
                  </span>
                  {/* <span>•</span>
                  <span>Owner: Lucas Nguyen</span> */}
                  <span>•</span>
                  <span>
                    Applied:{' '}
                    {/* {safeFormatDate(new Date(candidate.appliedAt), {
                      format: 'relative',
                    })} */}
                    <Tooltip
                      color="invert"
                      content={safeFormatDate(candidate.appliedAt, {
                        format: 'full',
                      })}
                    >
                      <span>
                        {safeFormatDate(candidate.appliedAt, {
                          format: 'relative',
                        })}
                      </span>
                    </Tooltip>
                  </span>
                </div>
              </div>

              {/* Application Details */}
              <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                {/* {(candidate.job as any)?.workTypes}
                <span>•</span> */}
                <span>
                  {[candidate.job?.city, candidate.job?.country]
                    .filter(Boolean)
                    .join(', ')}
                </span>
              </div>
            </div>
          </div>

          {/* Right Section - Status and Actions */}
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center lg:flex-col lg:items-end">
            {/* Status Dropdown */}
            {/* <div className="flex items-center gap-2">
              <Select
                value={displayStatus}
                onChange={handleStatusChange}
                options={statusOptions}
                className="min-w-[140px]"
                placeholder="Select status"
              />
            </div> */}

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              <Select
                value={currentStatus}
                onChange={handleStatusChange}
                options={statusOptions}
                className="min-w-[140px]"
                placeholder="Select status"
                prefix="Status:"
              />
              <Dropdown>
                <Dropdown.Trigger>
                  <MoreHorizontalIcon className="h-4 w-4" />
                </Dropdown.Trigger>
                <Dropdown.Menu>
                  <Dropdown.Item onClick={handleAddToShortList}>
                    Add to shortlist
                  </Dropdown.Item>
                  <Dropdown.Item onClick={handleExportPDF}>
                    Export as PDF
                  </Dropdown.Item>
                  <Dropdown.Item onClick={handleContact}>Contact</Dropdown.Item>
                  <Dropdown.Item onClick={handleShare}>Share</Dropdown.Item>
                </Dropdown.Menu>
              </Dropdown>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs Section */}
      <CadidateTabs candidate={candidate} />
    </div>
  );
}
