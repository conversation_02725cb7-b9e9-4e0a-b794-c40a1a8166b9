'use client';

import { debounce } from 'lodash';
import { orgAtom } from '@/store/organization-atom';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import { ApiListResponse, LIMIT, SelectOption } from '@/api-requests/types';
import { userAtom } from '@/store/user-atom';
import { useAtom } from 'jotai';
import { useRouter, useSearchParams } from 'next/navigation';
import { Suspense, useRef, useState } from 'react';
import CandidateFilter from './candidate-filter';
import CandidateTable from './candidate-table';
import { useGetCandidatesForAdmin } from '@/api-requests/job-candidate/get-candidate-for-admin';

interface IProps {}

function CandidateContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState<SelectOption | null>(null);
  const [search, setSearch] = useState<string>('');
  const [searchOrg, setSearchOrg] = useState<string>('');
  const [candidateSeleted, setCandidateSeleted] =
    useState<ShortlistCandidate | null>(null);
  const [openShortlist, setOpenShortlist] = useState(false);
  const [selectedCandidates, setSelectedCandidates] = useState<
    ShortlistCandidate[]
  >([]);

  const searchRef = useRef<HTMLInputElement>(null!);
  const searchOrgRef = useRef<HTMLInputElement>(null!);

  const { data, isLoading } = useGetCandidatesForAdmin({
    page,
    limit: LIMIT,
    candidateName: search,
    orgName: searchOrg,
    status: status?.value || '',
  });

  const handleChangeStatus = (option: SelectOption | null) => {
    setStatus(option);
    setPage(1);
  };

  const handleSearch = debounce((value: string) => {
    setSearch(value);
    setPage(1);
  }, 500);

  const handleSearchOrg = debounce((value: string) => {
    setSearchOrg(value);
    setPage(1);
  }, 500);

  const handleClickAction = (action: string, candidate: ShortlistCandidate) => {
    setCandidateSeleted(candidate);

    switch (action) {
      case 'Message':
        break;
      case 'Download CV':
        break;
      case 'Add to Shortlist':
        // setSelectedCandidates([]);
        break;
      case 'View Detail':
        // router.push(`/org/admin/candidates/${candidate._id}`);
        break;
      default:
        break;
    }
  };

  const handleCheckboxChange = (
    checked: boolean,
    candidate: ShortlistCandidate | string
  ) => {
    if (candidate === 'all') {
      if (checked) {
        setSelectedCandidates(data?.data || []);
      } else {
        setSelectedCandidates([]);
      }
      return;
    } else {
      setSelectedCandidates((prev) => {
        if (checked) {
          return [...prev, candidate as ShortlistCandidate];
        } else {
          return prev.filter(
            (c) => c._id !== (candidate as ShortlistCandidate)._id
          );
        }
      });
    }
  };

  return (
    <>
      <div className="flex flex-col gap-8">
        <div className="flex items-center justify-between gap-4">
          <h2 className="text-lg">Candidate list</h2>
        </div>

        <div className="flex flex-col gap-4">
          <CandidateFilter
            searchProps={{
              onChange: (e) => handleSearch(e.target.value),
              ref: searchRef,
            }}
            searchOrgProps={{
              onChange: (e) => handleSearchOrg(e.target.value),
              ref: searchOrgRef,
            }}
            statusProps={{
              value: status,
              onClear: () => setStatus(null),
              onChange: handleChangeStatus,
            }}
            selectedCandidates={selectedCandidates}
            totalCandidates={data?.meta?.total || 0}
            onHeartClick={() => setOpenShortlist(true)}
          />

          <CandidateTable
            jobCandidateData={data as ApiListResponse<ShortlistCandidate>}
            isLoading={isLoading}
            page={page}
            setPage={setPage}
            onClickAction={handleClickAction}
            onCheckboxChange={handleCheckboxChange}
            selectedCandidates={selectedCandidates}
          />
        </div>
      </div>
    </>
  );
}

export default function Candidate() {
  return (
    <Suspense fallback={<></>}>
      <CandidateContent />
    </Suspense>
  );
}
