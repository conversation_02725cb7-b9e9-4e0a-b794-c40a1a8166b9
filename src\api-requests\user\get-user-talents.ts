import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { UserQueryKeys, UserTalentParams, User } from './types';
import { ApiListResponse } from '../types';

export async function listUserTalents(
  payload: UserTalentParams
): Promise<ApiListResponse<User>> {
  const reps = await axiosInstance.post<ApiListResponse<User>>(
    API_ENDPONTS.USER_TALENT,
    payload
  );
  return reps.data;
}

export function useListUserTalents(payload: UserTalentParams) {
  return useQuery<ApiListResponse<User>>({
    queryKey: [UserQueryKeys.GET_USER_TALENTS, payload],
    queryFn: () => listUserTalents(payload),
  });
}
