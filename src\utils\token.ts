export function decodeToken(token: string) {
  try {
    const payload = JSON.parse(
      Buffer.from(token.split('.')[1], 'base64').toString('utf8')
    );

    return {
      expire: typeof payload?.exp === 'number' ? payload.exp : null,
      role: typeof payload?.role === 'string' ? payload.role : null,
      userId: typeof payload?.userId === 'string' ? payload.userId : null,
      email: typeof payload?.email === 'string' ? payload.email : null,
    };
  } catch {
    return null;
  }
}
