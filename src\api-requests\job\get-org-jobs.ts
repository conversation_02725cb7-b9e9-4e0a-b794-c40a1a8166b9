import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { Job, JobQueryKeys, OrgJobListParams } from './types';
import { cleanQueryParams } from '@/utils/url';
import { ApiListResponse } from '../types';

export async function listOrgJob(
  params: OrgJobListParams
): Promise<ApiListResponse<Job>> {
  const reps = await axiosInstance.get<ApiListResponse<Job>>(
    API_ENDPONTS.ORG_JOBS_LIST,
    {
      params: cleanQueryParams(params),
    }
  );
  return reps.data;
}

export function useListOrgJob(params: OrgJobListParams) {
  return useQuery<ApiListResponse<Job>>({
    queryKey: [JobQueryKeys.LIST_JOBS, params],
    queryFn: () => listOrgJob(params),
    enabled: !!params.orgId,
  });
}
