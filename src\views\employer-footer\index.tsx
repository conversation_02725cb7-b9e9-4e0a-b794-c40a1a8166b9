'use client';

import Image from 'next/image';

export default function HowItWorks() {
  const steps = [
    {
      step: 1,
      title: 'Post Your Job',
      description:
        'Create your job listing with our easy-to-use posting tool. Specify the skills you want to assess and choose simulation difficulty levels.',
      icon: '/employer/post-job.png',
    },
    {
      step: 2,
      title: 'Candidates Apply & Simulate',
      description:
        'Qualified candidates apply to your role and complete relevant job simulations that test the exact skills you need.',
      icon: '/employer/apply-simulate.png',
    },
    {
      step: 3,
      title: 'Review Results',
      description:
        'Access detailed simulation results, performance analytics, and candidate ranks through your employer dashboard.',
      icon: '/employer/review-results.png',
    },
    {
      step: 4,
      title: 'Interview Top Performers',
      description:
        'Focus your time on candidates who’ve already proven their abilities. Make confident hiring decisions based on demonstrated skills.',
      icon: '/employer/interview-top.png',
    },
  ];

  return (
    <section
      className="bg-cover bg-no-repeat py-16 text-white"
      style={{ backgroundImage: 'url("/employer/world-map.png")' }}
    >
      <div className="mx-auto flex max-w-[1200px] flex-col items-center justify-center gap-5 px-4 xl:px-0">
        <h2 className="text-center text-3xl font-bold md:text-4xl">
          How It Works
        </h2>
        <p className="text-center text-gray-300">
          Get started with simulation-based hiring in just a few simple steps
        </p>

        <div className="flex justify-center gap-4">
          {steps.map((_, i) => (
            <div key={i} className="flex items-center gap-3">
              <div className="bg-primary flex h-8 w-8 sm:h-10 sm:w-10 items-center justify-center rounded-full border-[3px] border-white text-sm font-bold text-white">
                {i + 1}
              </div>
              {i !== steps.length - 1 && (
                <Image
                  src="/employer/long-arrow.png"
                  alt="Arrow Right"
                  width={221}
                  height={16}
                  loader={({ src }) => src}
                  className="h-auto w-[50px] sm:w-[90px] md:w-[150px] lg:w-[221px]"
                />
              )}
            </div>
          ))}
        </div>

        <div className="relative z-20 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {steps.map((item, index) => (
            <div
              key={index}
              className="relative rounded-[2rem] bg-white p-6 pt-14 text-center text-gray-800 shadow-lg"
            >
              <Image
                src={item.icon}
                alt={item.title}
                className="mx-auto mb-4 h-[50px] w-[50px]"
                loader={({ src }) => src}
                width={50}
                height={50}
              />

              <h3 className="mb-2 text-base font-semibold leading-tight text-primary">
                {item.title}
              </h3>
              <p className="text-sm leading-relaxed text-gray-700">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
