import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { HomeSimulation, SimulationQueryKeys } from './types';

import { useQuery } from '@tanstack/react-query';

export async function getOrgPublicSimulation(params: {
  orgId: string;
  simId: string;
}): Promise<HomeSimulation> {
  const { orgId, simId } = params;
  const response = await requestGet<HomeSimulation>(
    API_ENDPONTS.GET_ORG_PUBLIC_SIMULATION,
    {},
    { orgId, simId }
  );
  return response.data;
}

export function useGetOrgPublicSimulation(params: {
  orgId: string;
  simId: string;
}) {
  return useQuery<HomeSimulation>({
    queryKey: [
      SimulationQueryKeys.GET_ORG_PUBLIC_SIMULATION,
      params.orgId,
      params.simId,
    ],
    queryFn: () => getOrgPublicSimulation(params),
    enabled: !!params.orgId && !!params.simId,
  });
}
