{"name": "IG-Career", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "start:dev": "next dev", "dev:uat": "dotenv -e .env.uat -- next dev", "build:uat": "dotenv -e .env.uat -- next build", "start:uat": "dotenv -e .env.uat -- next start", "dev:prod": "dotenv -e .env.production -- next dev", "build:prod": "dotenv -e .env.production -- next build", "start:prod": "dotenv -e .env.production -- next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tanstack/query-core": "^5.83.0", "@tanstack/react-query": "^5.83.0", "@types/react-datepicker": "^7.0.0", "axios": "^1.11.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "form-data": "^4.0.4", "jotai": "^2.12.5", "lodash": "^4.17.21", "lucide-react": "^0.540.0", "markdown-parser-react": "^2.0.2", "motion": "^12.23.12", "next": "15.4.2", "rc-pagination": "^5.1.0", "rc-slider": "^11.1.8", "react": "19.1.0", "react-countup": "^6.5.3", "react-datepicker": "^8.6.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "react-quill-new": "^3.6.0", "rizzui": "^1.0.1", "stream": "^0.0.3", "tailwind-merge": "^3.3.1", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/typography": "^0.5.16", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/react-query-devtools": "^5.83.0", "@types/lodash": "^4.17.20", "@types/node": "^20.19.9", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "dotenv-cli": "^9.0.0", "eslint": "^9.31.0", "eslint-config-next": "15.4.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.14", "typescript": "^5.8.3"}}