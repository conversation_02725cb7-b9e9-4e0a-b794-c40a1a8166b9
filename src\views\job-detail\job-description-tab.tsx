'use client';

import Image from 'next/image';
import Link from 'next/link';
import MoneyIcon from '../icons/money';
import { ClockIcon } from 'lucide-react';
import TechnologyIcon from '../icons/technology';
import { Job } from '@/api-requests/job';
import { getJobTypeString } from '../job/job-list';
interface IProps {
  job: Job;
}

export default function JobDescriptionTab({ job }: IProps) {
  const jobType = getJobTypeString(job || {});

  return (
    <div className="space-y-6 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
      <div>
        <div className="text-lg font-bold">{job.title}</div>

        <div className="flex items-center gap-4 text-sm text-gray-500">
          <span className="flex items-center gap-1">
            {job?.companyLogoUrl ? (
              <Image
                src={job?.companyLogoUrl}
                alt={job?.companyName || 'Company Logo'}
                width={50}
                height={50}
                className="rounded-full object-contain"
                loader={({ src }) => src}
              />
            ) : (
              <div
                className="rounded-full bg-gray-100"
                style={{ width: '100px', height: '100px' }}
              />
            )}
            <span>{job.companyName}</span>
          </span>
          <span className="flex items-center gap-1">
            <MoneyIcon className="h-5 w-5" />
            <span>
              {typeof job?.salary === 'string'
                ? job.salary
                : [job.salary?.min, job.salary?.max].join(' - ') || '-'}
            </span>
          </span>
          <span className="flex items-center gap-1">
            <ClockIcon className="h-5 w-5" />
            <span>{jobType || '-'}</span>
          </span>
          <span className="flex items-center gap-1">
            <TechnologyIcon className="h-5 w-5" />
            {Array.isArray(job?.categories) && job?.categories?.length > 0 ? (
              <span>{job.categories.join(', ')}</span>
            ) : (
              '-'
            )}
          </span>
        </div>
      </div>

      <section>
        <h2 className="mb-2 text-sm font-semibold text-gray-800">
          About this role
        </h2>
        <p
          className="text-justify text-sm text-gray-600"
          dangerouslySetInnerHTML={{
            __html: job.description || '',
          }}
        ></p>
      </section>

      {/* <section>
        <h2 className="mb-2 text-sm font-semibold text-gray-800">
          Qualification
        </h2>
        <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
          <li>0-2 years of experience in data analysis or a related role.</li>
          <li>
            Solid understanding of statistics, data wrangling, and data
            visualization.
          </li>
          <li>
            Proficiency in Excel and at least one analytics tool (e.g. SQL,
            Python, or R).
          </li>
          <li>
            Experience with data visualization tools like Tableau or Power BI is
            a plus.
          </li>
          <li>Good communication skills and an analytical mindset.</li>
        </ul>
      </section>

      <section>
        <h2 className="mb-2 text-sm font-semibold text-gray-800">
          Responsibility
        </h2>
        <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
          <li>
            Collect, clean, and analyze data from multiple sources to identify
            trends and insights.
          </li>
          <li>
            Build and maintain dashboards and regular reports for different
            teams.
          </li>
          <li>
            Assist in designing and conducting A/B testing or other data
            experiments.
          </li>
          <li>
            Collaborate with business and technical teams to define data
            requirements.
          </li>
          <li>
            Present findings and actionable insights in a clear and concise way.
          </li>
        </ul>
      </section> */}

      <section>
        <h2 className="mb-2 text-sm font-semibold text-gray-800">
          Additional Requirement
        </h2>
        <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
          <li>
            Candidates must complete the simulation provided by{' '}
            {job?.companyName || ''} and achieve a minimum score of 80% to
            proceed to the next stage of the recruitment process.
          </li>
        </ul>
      </section>
    </div>
  );
}
