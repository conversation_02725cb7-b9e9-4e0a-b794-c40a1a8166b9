'use client';

import SearchIcon from '@/views/icons/search';
import { Input, InputProps, Select, type SelectProps } from 'rizzui';
import { SelectOption } from '@/api-requests/types';
import { JobStatus } from '@/api-requests/job';
import { employementOptions } from '../job-creation/options';
import LocationIcon from '../icons/location';
import { difficultyOptions } from '../banner/job-banner/job-filter';

const matchOptions: SelectOption[] = [
  { label: 'Published', value: JobStatus.PUBLISHED },
  { label: 'Unpublished', value: JobStatus.UNPUBLISHED },
  { label: 'Draft', value: JobStatus.DRAFT },
  { label: 'Close', value: JobStatus.CLOSED },
];

const sortOptions: SelectOption[] = [
  { label: 'Candidates (ascending)', value: 'candidate_ascending' },
  { label: 'Candidates (descending)', value: 'candidate_descending' },
  { label: 'Created (newest)', value: 'created_newest' },
  { label: 'Created (oldest)', value: 'created oldest' },
  { label: 'Last apply (recent)', value: 'last_apply_recent' },
  { label: 'Last apply (older)', value: 'last_apply_older' },
];

interface IProps {
  searchProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  searchLocationProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  statusProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  jobTypeProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  simulationProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  sortByProps: {
    onClear: () => void;
    onChange: (option: SelectOption | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
  resetProps: React.ButtonHTMLAttributes<HTMLButtonElement>;
}

export default function AllJobsFilter({
  searchProps,
  statusProps,
  searchLocationProps,
  jobTypeProps,
  simulationProps,
  sortByProps,
  resetProps,
}: IProps) {
  return (
    <div className="flex flex-col gap-4 xl:flex-row xl:items-center xl:justify-between">
      <div className="flex flex-col gap-4 sm:flex-row">
        <Input
          variant="flat"
          prefix={<SearchIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Search job title"
          // className="w-full min-w-[300px] rounded-lg bg-white px-2 shadow-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
          className="w-full min-w-[300px] rounded-lg bg-white px-2 shadow-[0_4px_30px_rgba(0,0,0,0.15)] sm:w-auto [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
          inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
          size="sm"
          {...searchProps}
        />
        <Input
          variant="flat"
          prefix={<LocationIcon className="h-5 w-5 text-gray-400" />}
          placeholder="Search location"
          // className="w-full min-w-[300px] rounded-lg bg-white px-2 shadow-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
          className="w-full min-w-[300px] rounded-lg bg-white px-2 shadow-[0_4px_30px_rgba(0,0,0,0.15)] sm:w-auto [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
          inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
          size="sm"
          {...searchLocationProps}
        />
      </div>

      <div className="flex flex-col gap-3 sm:flex-row sm:items-center xl:justify-end">
        <div className="flex flex-wrap items-center gap-3">
          <div className="whitespace-nowrap text-sm opacity-50">Filter by:</div>

          <div className="flex flex-wrap gap-3">
            <Select
              clearable
              options={matchOptions}
              placeholder="Status"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
              size="sm"
              {...statusProps}
            />
            <Select
              clearable
              options={employementOptions}
              placeholder="Job type"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
              size="sm"
              {...jobTypeProps}
            />
            <Select
              clearable
              options={difficultyOptions}
              placeholder="Simulation"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
              size="sm"
              {...simulationProps}
            />
            <Select
              clearable
              options={sortOptions}
              placeholder="Sort by"
              className="w-full min-w-[130px] sm:w-auto [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
              size="sm"
              {...sortByProps}
            />
          </div>

          <button
            className="whitespace-nowrap text-sm text-slate-600 underline transition-colors hover:text-slate-900"
            {...resetProps}
          >
            Reset
          </button>
        </div>
      </div>
    </div>
  );
}
