'use client';

import <PERSON><PERSON>abe<PERSON> from '@/views/job-creation/field-label';
import { Controller, useFormContext } from 'react-hook-form';
import { Button, Text, Password } from 'rizzui';
import ArrowLeftIcon from '@/views/icons/arrow-left';

type FormValues = {
  password: string;
  newPassword: string;
};

interface IProps {
  onSetAction: (action: string) => void;
}

export default function ResetPasswordForm({ onSetAction }: IProps) {
  const {
    control,
    handleSubmit,
    formState: { errors, isValid, isSubmitting },
  } = useFormContext<FormValues>();

  const onSubmit = (data: FormValues) => {
    console.log('Submit with:', data);
  };

  return (
    <div className="space-y-8">
      <div>
        <button onClick={() => onSetAction('')}>
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div className="mt-2 font-bold">Reset Password</div>
        <div className="text-sm text-[#484848]">
          Please enter correct information to request a password change.
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <FieldLabel title="Password" />
          <Controller
            name="password"
            control={control}
            rules={{
              required: 'Vui lòng nhập mật khẩu',
              minLength: {
                value: 6,
                message: 'Mật khẩu phải có ít nhất 6 ký tự',
              },
            }}
            render={({ field }) => (
              <Password
                {...field}
                variant="flat"
                placeholder="Enter your password"
                className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
              />
            )}
          />
          {errors.password && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.password.message}
            </Text>
          )}
        </div>

        <div>
          <FieldLabel title="Confirm password" />
          <Controller
            name="newPassword"
            control={control}
            rules={{
              required: 'Please enter password',
              minLength: {
                value: 6,
                message: 'Password must be at least 6 characters',
              },
            }}
            render={({ field }) => (
              <Password
                {...field}
                variant="flat"
                placeholder="Enter your password"
                className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
                inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
              />
            )}
          />
          {errors.newPassword && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.newPassword.message}
            </Text>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <Button
          className="w-full bg-primary text-white"
          disabled={!isValid || isSubmitting}
          onClick={handleSubmit(onSubmit)}
        >
          Send request
        </Button>
      </div>
    </div>
  );
}
