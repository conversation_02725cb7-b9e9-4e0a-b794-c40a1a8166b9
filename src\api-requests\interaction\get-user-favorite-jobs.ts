import { cleanQueryParams } from '@/utils/url';
import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { InteractionQueryKeys } from './types';
import { Job } from '@/api-requests/job/types';
import { ApiListResponse } from '../types';

export async function getUserFavoriteJobs(
  userId: string,
  params: { page?: number; limit?: number }
): Promise<ApiListResponse<Job>> {
  const reps = await axiosInstance.get(
    API_ENDPONTS.USER_FAVORITE_JOBS.replace(':userId', userId),
    {
      params: cleanQueryParams(params),
    }
  );

  return reps.data;
}

export function useGetUserFavoriteJobs(
  userId: string,
  params: { page?: number; limit?: number }
) {
  return useQuery<ApiListResponse<Job>>({
    queryKey: [InteractionQueryKeys.USER_FAVORITE_JOBS, userId, params],
    queryFn: () => getUserFavoriteJobs(userId, params),
    enabled: !!userId,
  });
}
