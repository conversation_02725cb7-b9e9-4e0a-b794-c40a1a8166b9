export const cleanQuillContent = (content: string): string => {
  if (!content) return '';

  // Remove common empty Quill patterns
  const cleaned = content
    .replace(/<p><br><\/p>/g, '')
    .replace(/<p>\s*<\/p>/g, '')
    .replace(/<div><br><\/div>/g, '')
    .replace(/<div>\s*<\/div>/g, '')
    .replace(/^\s*$/, '')
    .trim();

  // If only whitespace or empty tags remain, return empty string
  if (cleaned === '' || cleaned.match(/^(<p>)*(\s|<br>)*(<\/p>)*$/)) {
    return '';
  }

  return content;
};
