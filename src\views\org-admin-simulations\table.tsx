'use client';

import { AdminSimulation } from '@/api-requests/simulation';
import { ApiListResponse, LIMIT } from '@/api-requests/types';
import cn from '@/utils/class-names';
import EditIcon from '@/views/icons/edit';
import EditSquareIcon from '@/views/icons/edit-square';
import PlusSquareIcon from '@/views/icons/plus-square';
import Pagination from '@/views/pagination';
import { useRouter } from 'next/navigation';
import { Badge, Button, Loader, Table, Tooltip } from 'rizzui';

const badgeClasses: Record<string, string> = {
  pending: 'bg-[#E8E8E8] text-[#161616]',
  published: 'bg-[#98FFDC] text-[#008457]',
  unpublished: 'bg-[#FFB8B8] text-[#A30000]',
};

interface IProps {
  data: ApiListResponse<AdminSimulation>;
  isLoading: boolean;
  page: number;
  setPage: (page: number) => void;
  refetch: () => void;
}

export default function OrgAdminSimulationsTable({
  data,
  isLoading,
  page,
  setPage,
  refetch,
}: IProps) {
  const router = useRouter();

  const handleView = (id: string) => {
    if (!id) return;
    router.push(`/org/admin/simulations/${id}`);
  };

  return (
    <>
      <div className="rounded-xl bg-white pb-5 shadow-md">
        <Table variant="modern">
          <Table.Header className="rounded-t-xl !bg-[#FAFAFA]">
            <Table.Row className="rounded-t-xl">
              <Table.Head>Simulation Name</Table.Head>
              <Table.Head>Participants</Table.Head>
              <Table.Head>Completions</Table.Head>
              <Table.Head>Status</Table.Head>
              <Table.Head className="!text-right">Actions</Table.Head>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {data?.data?.map((sim, index) => (
              <Table.Row key={sim._id}>
                <Table.Cell>
                  <div className="flex flex-row items-center gap-3">
                    <img
                      src={sim.banner || '/simulation/default-banner.png'}
                      alt={sim.name}
                      onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                        // Handle error case
                        e.currentTarget.src = '/simulation/default-banner.png';
                      }}
                      width={100}
                      // height={60}
                      className="object-fit rounded-lg"
                    />
                    <div>
                      <p className="text-xs font-semibold">{sim.name}</p>
                      <p className="line-clamp-2 max-w-80 text-xs">
                        {sim.description}
                      </p>
                    </div>
                  </div>
                </Table.Cell>

                <Table.Cell>
                  <span className="text-sm font-bold">{sim.joinedCount}</span>
                </Table.Cell>

                <Table.Cell>
                  <span className="text-sm font-bold">
                    {sim.completedCount}
                  </span>
                </Table.Cell>

                <Table.Cell>
                  <Badge
                    variant="flat"
                    size="sm"
                    className={cn(
                      badgeClasses[String(sim.status)],
                      'text-[12px] font-medium'
                    )}
                  >
                    {sim.status.charAt(0).toUpperCase() + sim.status.slice(1)}
                  </Badge>
                </Table.Cell>

                <Table.Cell className="text-right">
                  <div className="flex justify-end gap-3">
                    <Tooltip content="Edit" color="invert">
                      <Button
                        variant="text"
                        size="sm"
                        className="bg-[#F9F9F9] text-gray-500 hover:text-black"
                      >
                        <EditIcon className="h-4 w-4" />
                      </Button>
                    </Tooltip>
                    <Tooltip color="invert" content="Add">
                      <Button
                        variant="text"
                        size="sm"
                        className="bg-[#F9F9F9] text-gray-500 hover:text-black"
                        // onClick={() => handleView(job._id.toString())}
                      >
                        <PlusSquareIcon className="h-4 w-4" />
                      </Button>
                    </Tooltip>
                    <Tooltip color="invert" content="View">
                      <Button
                        variant="text"
                        size="sm"
                        className="bg-[#F9F9F9] text-gray-500 hover:text-black"
                        onClick={() => handleView(sim.simulationId)}
                      >
                        <EditSquareIcon className="h-4 w-4" />
                      </Button>
                    </Tooltip>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}

            {isLoading ? (
              <Table.Row>
                <Table.Cell colSpan={7} className="h-40 text-center">
                  <div className="flex min-h-96 items-center justify-center">
                    <Loader className="h-8 w-8" />
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : (
              data?.data?.length === 0 && (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center">
                    <div className="text-gray-500">No simulations found</div>
                  </Table.Cell>
                </Table.Row>
              )
            )}
          </Table.Body>
        </Table>

        <hr className="border-t border-gray-100 pt-4" />

        <Pagination
          total={data?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>
    </>
  );
}
