'use client';

import { useMemo, useState } from 'react';
import dynamic from 'next/dynamic';
import FieldLabel from './field-label';
import { Input, Select, Switch, Text } from 'rizzui';
import { Controller, useFormContext } from 'react-hook-form';
import { FormValues } from '.';
import ReactDatePicker from '../date-picker';
import { cleanQuillContent } from '@/utils/quill-editor';
import timezonesData from '@/data/timezones.json';
import { addDays } from 'date-fns';
import QuickQuestion from './quick-question';

export default function StepThree() {
  const QuillEditor = useMemo(
    () =>
      dynamic(() => import('@/views/quill-editor'), {
        ssr: false,
      }),
    []
  );

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext<FormValues>();

  const watchedQuickQuestions = watch('quickQuestions');

  const [enableQuestion, setEnableQuestion] = useState(watchedQuickQuestions?.length > 0);

  const currencyOptions = useMemo(
    () =>
      timezonesData.map((timezone) => ({
        label: timezone.id,
        value: timezone.id,
      })),
    []
  );

  const nextDay = addDays(new Date(), 1);

  return (
    <div className="space-y-4">
      <div>
        <div className="text-lg">
          Add detailed job description, responsibilities and terms
        </div>
        <div className="text-sm text-gray-500">
          Please fill in basic information for your job
        </div>
      </div>

      <div>
        <FieldLabel title="Add job description" />
        <Controller
          name="description"
          control={control}
          rules={{
            required: false,
          }}
          render={({ field }) => (
            <QuillEditor
              {...field}
              value={field.value || ''}
              onChange={(val) => {
                const cleanedContent = cleanQuillContent(val);
                field.onChange(cleanedContent);
              }}
              containerOptions={[['link']]}
              className="[&>.ql-container_.ql-editor]:min-h-[100px]"
            />
          )}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <div className="w-full">
          <FieldLabel title="Expires At" />
          <Controller
            name="expiresAt"
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <ReactDatePicker
                selected={field.value}
                onChange={(date) => field.onChange(date)}
                placeholderText="Select Date"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0 [&_.react-datepicker-wrapper]:!w-full"
                isClearable
                minDate={nextDay}
                showTimeSelect
              />
            )}
          />
        </div>

        <div className="w-full">
          <FieldLabel title="Timezone" />
          <Controller
            name="timezone"
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <Select
                clearable
                {...field}
                onClear={() => field.onChange(null)}
                options={currencyOptions}
                placeholder="Select timezone"
                className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                searchable
              />
            )}
          />
        </div>

        <div>
          <FieldLabel title="Application Limit" />
          <Controller
            name="applicationLimit"
            control={control}
            rules={{
              required: false,
              min: {
                value: 1,
                message: 'Minimum limit is 1',
              },
              validate: (value) =>
                !value ||
                Number.isInteger(Number(value)) ||
                'Please enter a valid number',
            }}
            render={({ field }) => (
              <Input
                {...field}
                value={field.value || ''}
                onChange={(e) => {
                  const val = e.target.value;
                  // Allow only numbers
                  if (val === '' || /^\d+$/.test(val)) {
                    field.onChange(val === '' ? null : Number(val));
                  }
                }}
                type="text"
                variant="flat"
                placeholder="Application limit (leave empty for unlimited)"
                className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
              />
            )}
          />
          {errors.applicationLimit && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.applicationLimit.message}
            </Text>
          )}
        </div>
      </div>

      <div>
        <Switch
          variant="flat"
          label={'Add questions for job'}
          checked={enableQuestion}
          onChange={(e) => setEnableQuestion(e.target.checked)}
        />
        <div className="text-sm text-gray-500">
          Questions will be displayed when users apply, helping to gather
          additional information during the application process.
        </div>
      </div>

      {enableQuestion && (
        <div>
          <QuickQuestion />
        </div>
      )}

      <div>
        <div>Job Simulation</div>
        <div className="text-sm text-gray-500">
          Job Simulation will be automatically generated based on the job
          description and requirements you create.
        </div>
      </div>
    </div>
  );
}
