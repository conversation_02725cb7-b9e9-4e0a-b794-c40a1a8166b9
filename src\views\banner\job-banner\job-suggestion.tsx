'use client';

import Image from 'next/image';
import MoneyIcon from '@/views/icons/money';
import ClockIcon from '@/views/icons/clock';
import TechnologyIcon from '@/views/icons/technology';
import HeartOutlineIcon from '@/views/icons/heart-outline';
import {
  getJobTypeString,
  levelStyle,
  simulationLevel,
} from '@/views/job/job-list';
import { Job, useListJob } from '@/api-requests/job';
import cn from '@/utils/class-names';
import { safeFormatDistanceToNow } from '@/utils/date';
import { LIMIT } from '@/api-requests/types';
import JobCard from './job-card';
import Loading from './loading';

interface JobCardProps {
  job: Job;
  setSelectedJob: (job: Job) => void;
  selectedJob?: Job | null;
}

export default function JobSuggestion() {
  const {
    data: jobData,
    isLoading,
    refetch,
  } = useListJob({
    limit: 4,
    page: 1,
  });

  return (
    <div className="z-10 mx-auto w-full max-w-[1440px] px-4 xl:px-0">
      <div className="mb-4 text-xl font-bold">Role You Can Try Now</div>
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-2">
        {isLoading ? (
          <Loading />
        ) : (
          jobData?.data.map((job, index) => <JobCard key={index} job={job} />)
        )}
      </div>
    </div>
  );
}
