import React from 'react';

function MoneyIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="13"
      height="12"
      viewBox="0 0 13 12"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_3183_32593)">
        <path
          d="M2.91406 6H3.91406M9.91406 6H10.9141M1.16406 8.25V10H1.41406C1.91174 9.92077 2.41205 9.85907 2.91406 9.815M1.16406 8.25H1.41406C1.81189 8.25 2.19342 8.40804 2.47472 8.68934C2.75603 8.97064 2.91406 9.35218 2.91406 9.75V9.815M1.16406 8.25V3.75M2.91406 9.815C4.24413 9.69547 5.57865 9.63208 6.91406 9.625C7.98106 9.625 9.55456 9.6885 10.9141 9.815M1.16406 3.75V2H1.41406C1.91174 2.07923 2.41205 2.14093 2.91406 2.185M1.16406 3.75H1.41406C1.81189 3.75 2.19342 3.59196 2.47472 3.31066C2.75603 3.02936 2.91406 2.64782 2.91406 2.25V2.185M10.9141 9.815C11.4161 9.85907 11.9164 9.92077 12.4141 10H12.6641V8.25M10.9141 9.815V9.75C10.9141 9.35218 11.0721 8.97064 11.3534 8.68934C11.6347 8.40804 12.0162 8.25 12.4141 8.25H12.6641M12.6641 8.25V3.75M12.6641 3.75V2H12.4141C11.9164 2.07923 11.4161 2.14093 10.9141 2.185M12.6641 3.75H12.4141C12.0162 3.75 11.6347 3.59196 11.3534 3.31066C11.0721 3.02936 10.9141 2.64782 10.9141 2.25V2.185M10.9141 2.185C9.58399 2.30453 8.24947 2.36792 6.91406 2.375C5.57865 2.36792 4.24413 2.30453 2.91406 2.185M6.91406 7.25C6.58254 7.25 6.2646 7.1183 6.03018 6.88388C5.79576 6.64946 5.66406 6.33152 5.66406 6C5.66406 5.66848 5.79576 5.35054 6.03018 5.11612C6.2646 4.8817 6.58254 4.75 6.91406 4.75C7.24558 4.75 7.56353 4.8817 7.79795 5.11612C8.03237 5.35054 8.16406 5.66848 8.16406 6C8.16406 6.33152 8.03237 6.64946 7.79795 6.88388C7.56353 7.1183 7.24558 7.25 6.91406 7.25Z"
          stroke="#484848"
          strokeWidth="0.8"
        />
      </g>
      <defs>
        <clipPath id="clip0_3183_32593">
          <rect
            width="12"
            height="12"
            fill="white"
            transform="translate(0.914062)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}

export default MoneyIcon;
