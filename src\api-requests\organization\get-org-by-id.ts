import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';
import { useQuery } from '@tanstack/react-query';
import { Organization, OrganizationQueryKeys } from './types';
import { setOrgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';

export async function getOrganizationById(
  orgId: string
): Promise<Organization> {
  const response = await axiosInstance.get<Organization>(
    API_ENDPONTS.GET_ORG_BY_ID.replace(':orgId', orgId)
  );
  return response.data;
}

export function useGetOrganizationById(orgId: string) {
  const [, setOrg] = useAtom(setOrgAtom);

  return useQuery<Organization>({
    queryKey: [OrganizationQueryKeys.GET_ORG_BY_ID, orgId],
    queryFn: () => getOrganizationById(orgId),
    enabled: !!orgId,
    select: (data) => {
      setOrg(data);
      return data;
    },
  });
}
