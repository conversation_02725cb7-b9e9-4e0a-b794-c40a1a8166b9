import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { JobListResponse, JobListParams, JobQueryKeys } from './types';
import { cleanQueryParams } from '@/utils/url';

export async function listJob(params: JobListParams): Promise<JobListResponse> {
  const reps = await axiosInstance.get<JobListResponse>(
    API_ENDPONTS.JOBS_LIST,
    {
      params: cleanQueryParams(params),
    }
  );
  return reps.data;
}

export function useListJob(params: JobListParams) {
  const newQueryKey = { ...params };
  delete newQueryKey.id;
  return useQuery<JobListResponse>({
    queryKey: [JobQueryKeys.LIST_JOBS, newQueryKey],
    queryFn: () => listJob(params),
  });
}
