import { orgAtom } from '@/store/organization-atom';
import FieldLabel from '@/views/job-creation/field-label';
import { useAtom } from 'jotai';

interface RowProps {
  label: string;
  shortDescription?: React.ReactNode;
  value?: React.ReactNode;
}

function InfoRow({ label, shortDescription, value }: RowProps) {
  return (
    <div className="flex flex-col items-start gap-y-2 border-b border-gray-200 py-5 sm:flex-row sm:items-center sm:gap-y-0">
      <div className="w-full pr-4 sm:w-1/4">
        <FieldLabel title={label || ''} />
        {shortDescription}
      </div>

      <div className="relative w-full sm:w-3/4">
        <div>{value || '-'}</div>
      </div>
    </div>
  );
}

export default function CompanyInfo() {
  const [org, setOrg] = useAtom(orgAtom);

  return (
    <div>
      <InfoRow label="Company Name" value={org?.name} />
      <InfoRow label="Location" value={org?.location} />
      <InfoRow label="Website" value={org?.website} />
      <InfoRow label="Email" value={org?.email} />
      <InfoRow
        label="Short description"
        value={org?.description}
        shortDescription={
          <div className="text-[12px] text-gray-500">
            Write a short overview of your company for your public profile.
          </div>
        }
      />
    </div>
  );
}
