'use client';

export default function Loading() {
  return (
    <div className="col-span-12 grid grid-cols-1 gap-6 lg:grid-cols-12">
      {/* Left Column - Job List Skeletons */}
      <div className="space-y-4 lg:col-span-4">
        {[...Array(4)].map((_, index) => (
          <div
            key={index}
            className="w-full animate-pulse rounded-lg bg-gray-100 p-4 shadow-md"
          >
            <div className="h-[20px] w-full animate-pulse rounded-lg bg-gray-300"></div>
            <div className="my-4 border-t border-gray-200"></div>
            <div className="h-[80px] w-full animate-pulse rounded-lg bg-black/5"></div>
            <div className="my-4 border-t border-gray-200"></div>
            <div className="h-[30px] w-full animate-pulse rounded-lg bg-gray-300"></div>
          </div>
        ))}
      </div>

      {/* Right Column - Job Detail Skeleton */}
      <div className="mt-4 lg:col-span-8 lg:mt-0">
        <div className="w-full animate-pulse rounded-lg bg-gray-100 p-6 shadow-md">
          {/* Header */}
          <div className="mb-6">
            <div className="mb-4 flex items-center gap-4">
              {/* Avatar skeleton */}
              <div className="h-12 w-12 flex-shrink-0 animate-pulse rounded-full bg-gray-300"></div>

              {/* Name and Description in 2 columns */}
              <div className="grid flex-1 grid-cols-2 gap-4">
                {/* Left column - Name */}
                <div>
                  <div className="mb-1 h-5 w-32 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-24 animate-pulse rounded bg-gray-200"></div>
                </div>

                {/* Right column - Description/Details */}
                <div>
                  <div className="mb-1 h-5 w-28 animate-pulse rounded bg-gray-300"></div>
                  <div className="h-3 w-20 animate-pulse rounded bg-gray-200"></div>
                </div>
              </div>
            </div>

            {/* Additional info row */}
            <div className="h-4 w-2/3 animate-pulse rounded bg-gray-200"></div>
          </div>

          {/* Content sections */}
          <div className="space-y-6">
            {/* Job description section */}
            <div>
              <div className="mb-3 h-5 w-1/4 animate-pulse rounded-lg bg-gray-300"></div>
              <div className="space-y-2">
                <div className="h-4 w-full animate-pulse rounded-lg bg-gray-200"></div>
                <div className="h-4 w-full animate-pulse rounded-lg bg-gray-200"></div>
                <div className="h-4 w-3/4 animate-pulse rounded-lg bg-gray-200"></div>
              </div>
            </div>

            {/* Requirements section */}
            <div>
              <div className="mb-3 h-5 w-1/3 animate-pulse rounded-lg bg-gray-300"></div>
              <div className="space-y-2">
                <div className="h-4 w-full animate-pulse rounded-lg bg-gray-200"></div>
                <div className="h-4 w-5/6 animate-pulse rounded-lg bg-gray-200"></div>
                <div className="h-4 w-4/5 animate-pulse rounded-lg bg-gray-200"></div>
              </div>
            </div>

            {/* Benefits section */}
            <div>
              <div className="mb-3 h-5 w-1/4 animate-pulse rounded-lg bg-gray-300"></div>
              <div className="space-y-2">
                <div className="h-4 w-full animate-pulse rounded-lg bg-gray-200"></div>
                <div className="h-4 w-3/4 animate-pulse rounded-lg bg-gray-200"></div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex gap-3 pt-4">
              <div className="h-10 w-32 animate-pulse rounded-lg bg-gray-300"></div>
              <div className="h-10 w-28 animate-pulse rounded-lg bg-gray-200"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
