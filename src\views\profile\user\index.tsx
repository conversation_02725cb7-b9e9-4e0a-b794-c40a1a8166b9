'use client';

import {
  type UserProfile,
  useGetUserProfile,
} from '@/api-requests/user-profile';
import { userAtom } from '@/store/user-atom';
import cn from '@/utils/class-names';
import { useAtom } from 'jotai';
import { useParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import ProfileTab from './profile-tab';
import ProfileTabs from './profile-tabs';

// const tabs = ['Profile', 'CV'];

const profileTabs = [
  { name: 'Profile', hash: `profile` },
  { name: 'CV', hash: `cv` },
  { name: 'My Applications', hash: `applications` },
];

export default function UserProfile() {
  const { id: userId } = useParams();
  const [user] = useAtom(userAtom);

  const [activeTab, setActiveTab] = useState('profile');
  const tabs =
    user?.id !== userId
      ? profileTabs.filter((tab) => tab.hash === 'profile')
      : profileTabs;

  const { data, isLoading, refetch } = useGetUserProfile(userId as string);

  useEffect(() => {
    const onHashChange = () => {
      const hash = window.location.hash.replace(/^#/, '');
      if (profileTabs.map((t) => t.hash).includes(hash)) {
        setActiveTab(hash);
      } else {
        setActiveTab('profile');
      }
    };
    window.addEventListener('hashchange', onHashChange);

    onHashChange();

    return () => window.removeEventListener('hashchange', onHashChange);
  }, []);

  const handleTabClick = (tab: string) => {
    window.location.hash = tab;
    setActiveTab(tab);
  };

  return (
    <div className="flex flex-col gap-6 lg:flex-row lg:gap-6">
      <div className={cn('lg:flex-shrink-0', isLoading && 'blur-[3px]')}>
        <ProfileTabs
          setActiveTab={handleTabClick}
          activeTab={activeTab}
          tabs={tabs}
          profile={(data || user) as UserProfile}
        />
      </div>
      <div className={cn('flex-1', isLoading && 'blur-[3px]')}>
        <ProfileTab
          activeTab={activeTab}
          tabs={tabs}
          refetch={refetch}
          profile={(data || user) as UserProfile}
        />
      </div>
    </div>
  );
}
