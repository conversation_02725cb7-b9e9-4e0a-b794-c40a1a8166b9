import { API_ENDPONTS } from '@/config/endpoint';
import { setOrgAtom } from '@/store/organization-atom';
import { requestGet } from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { useAtom } from 'jotai';
import { Organization, OrganizationQueryKeys } from './types';

export async function getOrganizationByAdmin(
  orgId: string
): Promise<Organization> {
  console.log('orgId ::: ', orgId);
  const response = await requestGet<Organization>(
    API_ENDPONTS.GET_ORG_BY_ADMIN,
    {},
    { orgId }
  );
  return response.data;
}

export function useGetOrganizationByAdmin(orgId: string) {
  const [, setOrg] = useAtom(setOrgAtom);

  return useQuery<Organization>({
    queryKey: [OrganizationQueryKeys.GET_ORG_BY_ID, orgId],
    queryFn: () => getOrganizationByAdmin(orgId),
    enabled: !!orgId,
    select: (data) => {
      setOrg(data);
      return data;
    },
  });
}
