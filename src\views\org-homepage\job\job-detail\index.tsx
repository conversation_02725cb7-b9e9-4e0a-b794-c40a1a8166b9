'use client';

import { useGetOrgPublicJob } from '@/api-requests/job/get-org-home-job';
import ApplyCVButton from '@/components/ApplyCVButton';
import StartSimulationButton from '@/components/StartSimulationButton';
import { orgAtom } from '@/store/organization-atom';
import { safeFormatDistanceToNow } from '@/utils/date';
import CalendarIcon from '@/views/icons/calendar';
import ClockIcon from '@/views/icons/clock';
import { MapMarker } from '@/views/icons/mapmarker';
import MoneyIcon from '@/views/icons/money';
import { getJobTypeString } from '@/views/job/job-list';
import { useAtom } from 'jotai';
import { ArrowLeft } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from 'rizzui/button';
import { Tooltip } from 'rizzui/tooltip';

const Loading = () => {
  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"></div>
    </div>
  );
};

const NoData = () => {
  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <img src="/error/404.png" alt="Not found" />
    </div>
  );
};

export default function OrgHomePageJobDetail() {
  const { jobId } = useParams<{ jobId: string }>();
  const router = useRouter();

  const [org] = useAtom(orgAtom);

  const {
    data: job,
    isFetched,
    refetch: refetchJob,
  } = useGetOrgPublicJob({
    orgId: org?._id || '',
    jobId,
  });

  const formattedJobDate = job
    ? safeFormatDistanceToNow(new Date(job.postedTime), {
        addSuffix: true,
      })
    : '';

  const jobType = job ? getJobTypeString(job) : '';

  const handleBackToList = () => {
    // Navigate to /partner/<orgId>
    router.push(`/org/${org?._id}`);
  };

  if (!isFetched) return <Loading />;

  if (!job) return <NoData />;

  return (
    <div>
      <Button
        className="mb-6 flex items-center px-0 font-semibold"
        variant="flat"
        onClick={handleBackToList}
      >
        <ArrowLeft className="mr-2" size={18} />
        Back
      </Button>
      <div className="mx-auto space-y-6">
        <div className="grid gap-6 lg:grid-cols-12">
          <div className="col-span-12 flex min-h-28 flex-col items-start rounded-2xl bg-white p-4 shadow lg:col-span-7">
            <h2 className="text-xl font-semibold">{job.title}</h2>
            <div className="mt-4 flex flex-wrap items-center gap-4 text-sm text-[#484848]">
              <span className="flex items-center space-x-1">
                <CalendarIcon className="h-4 w-4" />
                <span>{formattedJobDate}</span>
              </span>
              <span className="flex items-center space-x-1">
                <MoneyIcon className="h-4 w-4" />
                <span>
                  {typeof job?.salary === 'string'
                    ? job.salary
                    : [job.salary?.min, job.salary?.max].join(' - ') || '-'}
                </span>
              </span>
              <span className="flex items-center space-x-1">
                <ClockIcon className="h-4 w-4" />
                <span>{jobType}</span>
              </span>
            </div>
          </div>

          <div className="col-span-12 flex min-h-28 items-start gap-3 rounded-2xl bg-white p-4 shadow lg:col-span-5">
            <img
              src={job.org.logo || ''}
              alt="owner"
              className="h-16 w-16 rounded-full"
            />
            <div>
              <h2 className="text-xl font-semibold">{job.org.name}</h2>
              <div className="mt-4 flex flex-wrap items-center gap-4 text-sm text-[#484848]">
                <span className="flex items-center space-x-1">
                  <MapMarker className="h-4 w-4" />
                  <Tooltip color="invert" content={job.location}>
                    <span>
                      {job.org.city}, {job.org.country}
                    </span>
                  </Tooltip>
                </span>
              </div>
            </div>
          </div>
        </div>

        <h2 className="font-bold">Job Description</h2>
        <div className="space-y-6 rounded-2xl bg-white p-6 shadow">
          <div className="flex flex-wrap items-center justify-between gap-2">
            <h3 className="text-lg font-semibold">{job.title}</h3>
            <div className="flex space-x-2">
              {(job as any).progress?.status === 'completed' ? (
                <Button
                  className="font-bold"
                  variant="text"
                  onClick={() => {
                    // TODO: goto application details
                  }}
                >
                  Applied
                </Button>
              ) : (
                <>
                  {['cv', 'all'].includes(job?.applyMode as string) && (
                    <ApplyCVButton
                      jobId={job.jobId || ''}
                      simId={job.simulation?.id || ''}
                      onSuccess={() => {
                        refetchJob();
                      }}
                      onErrorJobApplied={() => {
                        refetchJob();
                      }}
                      buttonProps={{ variant: 'outline' }}
                    >
                      Apply with CV
                    </ApplyCVButton>
                  )}
                  {['simulation', 'all'].includes(job?.applyMode as string) && (
                    <>
                      <StartSimulationButton
                        simId={job.simulation?.id || ''}
                        jobId={job.jobId || ''}
                        onErrorJobApplied={() => {
                          refetchJob();
                        }}
                        buttonProps={{
                          className: 'bg-primary text-white',
                          disabled: !job.simulation?.id,
                        }}
                      >
                        Apply with Simulation
                      </StartSimulationButton>
                    </>
                  )}
                </>
              )}
            </div>
          </div>

          <div>
            <h4 className="mb-2 font-semibold">About this role</h4>
            <p
              className="text-justify"
              dangerouslySetInnerHTML={{ __html: job.description || '' }}
            ></p>
          </div>
        </div>
      </div>
    </div>
  );
}
