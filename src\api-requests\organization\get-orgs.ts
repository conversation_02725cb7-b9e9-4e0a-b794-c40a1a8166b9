import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';
import { useQuery } from '@tanstack/react-query';
import { GetOrgParams, Organization, OrganizationQueryKeys } from './types';
import { setOrgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';
import { cleanQueryParams } from '@/utils/url';

export async function getOrganizations(
  payload: GetOrgParams
): Promise<Organization[]> {
  const response = await axiosInstance.get<Organization[]>(
    API_ENDPONTS.GET_ORG,
    { params: cleanQueryParams(payload) }
  );
  return response.data;
}

export function useGetOrganizations(payload: GetOrgParams) {
  return useQuery<Organization[]>({
    queryKey: [OrganizationQueryKeys.GET_ORG, payload],
    queryFn: () => getOrganizations(payload),
    enabled: true,
  });
}
