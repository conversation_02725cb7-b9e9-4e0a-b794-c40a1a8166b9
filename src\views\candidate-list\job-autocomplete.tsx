'use client';

import { Job } from '@/api-requests/job';
import { useListOrgJob } from '@/api-requests/job/get-org-jobs';
import { SelectOption } from '@/api-requests/types';
import { orgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';
import { debounce } from 'lodash';
import { useRef, useState } from 'react';
import { Select } from 'rizzui';

interface IProps {
  onSelectJob: (jobId: string | null) => void;
  limit?: number;
}

const JobAutoComplete = ({ onSelectJob, limit = 10 }: IProps) => {
  const [org] = useAtom(orgAtom);
  const [selectedJob, setSelectedJobId] = useState<SelectOption | null>(null);
  const [keyword, setKeyword] = useState<string>('');

  const debounceRef = useRef(
    debounce((newKeyword: string) => {
      setKeyword(newKeyword);
    }, 500)
  );

  const { data } = useListOrgJob({
    orgId: (org?._id || '') as string,
    page: 1,
    limit,
    title: keyword,
  });

  const buildOptions = () => {
    return (data?.data || []).map((job: Job) => ({
      label: job.title,
      value: job.jobId || job._id,
    }));
  };

  const handleSearchChange = (keyword: string) => {
    if (keyword && keyword.trim().length < 3) return;
    debounceRef.current(keyword);
  };

  const handleSelectJob = (jobOption: SelectOption | null) => {
    setSelectedJobId(jobOption);
    onSelectJob(jobOption?.value || null);
  };

  return (
    <Select
      clearable
      value={selectedJob}
      searchable={true}
      options={buildOptions()}
      onChange={handleSelectJob}
      placeholder="Jobs"
      className="w-full"
      size="md"
      onClear={() => handleSelectJob(null)}
      onSearchChange={(keyword: string) => handleSearchChange(keyword)}
    />
  );
};

export default JobAutoComplete;
