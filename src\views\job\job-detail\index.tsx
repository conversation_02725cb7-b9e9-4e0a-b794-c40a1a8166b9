'use client';

import { Job } from '@/api-requests/job';
import { useApplySimulation } from '@/api-requests/job/apply-simulation';
import { useCheckApplied } from '@/api-requests/job/check-applied';
import { Role } from '@/api-requests/user/types';
import { useURLParamsManager } from '@/hooks/use-update-url-params';
import { userAtom, UserInfo } from '@/store/user-atom';
import cn from '@/utils/class-names';
import { safeFormatDistanceToNow } from '@/utils/date';
import { CAREER_AT_TOKEN, getClientSideCookie } from '@/utils/http-client';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import ClockIcon from '@/views/icons/clock';
import CopyIcon from '@/views/icons/copy';
import HeartOutlineIcon from '@/views/icons/heart-outline';
import MoneyIcon from '@/views/icons/money';
import TechnologyIcon from '@/views/icons/technology';
import {
  getJobTypeString,
  levelStyle,
  simulationLevel,
} from '@/views/job/job-list';
import { useAtom } from 'jotai';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { Button } from 'rizzui';
import CVApplyEnhancedModal from './apply-cv-dialog-enhanced';
import HeartIcon from '@/views/icons/heart';

interface JobDetailProps {
  job: Job;
  isLoading: boolean;
  creatingSimulation: boolean;
  onFavorite: (job: Job) => void;
}

export default function JobDetail({
  job,
  isLoading,
  creatingSimulation,
  onFavorite,
}: JobDetailProps) {
  const urlManager = useURLParamsManager();
  const formattedDate = safeFormatDistanceToNow(new Date(job?.postedTime), {
    addSuffix: true,
  });
  const jobType = getJobTypeString(job);
  const [user] = useAtom(userAtom);

  const [openLoginModal, setOpenLoginModal] = useState(false);
  const [applyCVModal, setApplyCVModal] = useState(false);
  const [applyingMode, setApplyingMode] = useState<'cv' | 'simulation' | ''>(
    ''
  );
  const { mutateAsync: applySimulation } = useApplySimulation();

  const { data: isApplied, refetch: refetchIsApplied } = useCheckApplied(
    job.jobId,
    job.simulation?.id
  );

  const goToSimLink = async () => {
    // TODO: check allow apply with simulation or not
    if (!job.simulation?.id) return '';

    const link = await applySimulation({
      jobId: job.jobId,
      simulationId: job?.simulation?.id || '',
    });

    if (link) {
      window.open(link, '_blank');
      setApplyingMode('');
    } else {
      toast.error('Failed to apply. Please try again later.');
    }
  };

  const checkUserLogin = (mode: 'simulation' | 'cv' | '') => {
    const accessToken = getClientSideCookie(CAREER_AT_TOKEN);
    if (!accessToken || !user) {
      setOpenLoginModal(true);
      setApplyingMode(mode);
      return false;
    }
    if (user?.role !== Role.USER) {
      toast.error('Only users can apply for this job.');
      return false;
    }

    return true;
  };

  const handleApplyCV = () => {
    if (!checkUserLogin('cv')) return;
    setApplyCVModal(true);
  };

  const handleStartSimulation = () => {
    if (!checkUserLogin('simulation')) return;

    goToSimLink();
  };

  const handleLoginSuccess = (respUser?: UserInfo) => {
    if (applyingMode === 'simulation') {
      goToSimLink();
    } else if (applyingMode === 'cv') {
      setApplyCVModal(true);
    }
  };

  const handleCopyLink = () => {
    const paramsURL = urlManager.copy({ id: job.jobId });
    const shareLink = window.location.origin + paramsURL;
    navigator.clipboard.writeText(shareLink);
    toast.success('Job link copied to clipboard!');
  };

  return (
    <>
      <div
        className={cn(
          'mx-auto space-y-6 rounded-[24px] border-[1px] border-[#E8E8E8] bg-white p-4',
          isLoading && 'blur-[3px]'
        )}
      >
        {/* Header */}
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h1 className="line-clamp-2 text-xl font-semibold text-gray-800">
              {job?.title}
            </h1>
            <div className="mt-2 flex items-center gap-3 text-xs text-gray-500">
              {job.simulation?.id && (
                <span className="rounded-full border border-primary bg-[#CCFFE7] px-2 py-0.5 text-primary">
                  Simulation Available
                </span>
              )}
              <span className="text-[12px] text-gray-500">
                <b>{job?.applicants}</b> people applied
              </span>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <button
              className="group flex h-8 w-8 items-center justify-center rounded-[8px] bg-[#F9F9F9] hover:bg-primary"
              onClick={handleCopyLink}
            >
              <CopyIcon className="h-5 w-5 group-hover:text-white" />
            </button>
            <button
              className="group flex h-8 w-8 items-center justify-center rounded-[8px] bg-[#F9F9F9] hover:bg-primary"
              onClick={(e) => {
                e.stopPropagation();
                onFavorite(job);
              }}
              disabled={isLoading}
            >
              {job?.interaction?.value ? (
                <HeartIcon className="h-5 w-5 group-hover:text-white" />
              ) : (
                <HeartOutlineIcon className="h-5 w-5 group-hover:text-white" />
              )}
            </button>
            {/* TODO: when click, navigate user to the history (in profile page) */}
            {isApplied ? (
              <span className="text-sm font-semibold text-green-500">
                Applied
              </span>
            ) : (
              <>
                {['cv', 'all'].includes(job?.applyMode as string) && (
                  <Button
                    size="sm"
                    className="bg-primary text-white"
                    onClick={handleApplyCV}
                  >
                    Apply with CV
                  </Button>
                )}
                {['simulation', 'all'].includes(job?.applyMode as string) && (
                  <Button
                    size="sm"
                    className="bg-primary text-white"
                    disabled={!job.simulation?.id || creatingSimulation}
                    onClick={handleStartSimulation}
                  >
                    Apply with Simulation
                  </Button>
                )}
              </>
            )}
          </div>
        </div>

        {/* Company Info */}
        <div className="flex items-center gap-4 border-t border-gray-200 pt-4">
          {job?.companyLogoUrl ? (
            <Image
              src={job?.companyLogoUrl}
              alt={job?.companyName || 'Company Logo'}
              width={100}
              height={100}
              className="rounded-full object-contain"
              loader={({ src }) => src}
            />
          ) : (
            <div
              className="rounded-full bg-gray-100"
              style={{ width: '100px', height: '100px' }}
            />
          )}

          <div>
            <p className="text-sm font-medium text-gray-700">
              {job?.orgId ? (
                <Link
                  href={`/org/${job?.orgId}`}
                  target="_blank"
                  className="hover:underline"
                >
                  {job?.companyName}
                </Link>
              ) : (
                job?.companyName
              )}
            </p>
            <p className="text-[12px] text-gray-500">
              {job?.location} • {formattedDate}
            </p>
            <div className="mt-2 flex items-center gap-2 text-[10px] text-gray-500">
              <span className="flex items-center gap-1">
                <MoneyIcon className="h-3 w-3" />
                <span>
                  {typeof job?.salary === 'string'
                    ? job.salary
                    : [job.salary?.min, job.salary?.max].join(' - ') || '-'}
                </span>
              </span>
              <span className="flex items-center gap-1">
                <ClockIcon className="h-3 w-3" />
                <span>{jobType || '-'}</span>
              </span>
              <span className="flex items-center gap-1">
                <TechnologyIcon className="h-3 w-3" />
                {Array.isArray(job?.categories) &&
                job?.categories?.length > 0 ? (
                  <span>{job.categories.join(', ')}</span>
                ) : (
                  '-'
                )}
              </span>
              {job?.simulation?.level && (
                <span
                  className={`${levelStyle[simulationLevel[Number(job.simulation.level)]]} whitespace-nowrap rounded-full px-1 py-0.5`}
                >
                  {simulationLevel[Number(job.simulation.level)]}:{' '}
                  <b>{job.simulation.minute} mins</b>
                </span>
              )}
            </div>
          </div>
        </div>

        {/* About this role */}
        <section>
          <h2 className="mb-2 text-sm font-semibold text-gray-800">
            About this role
          </h2>
          <p
            className="text-justify text-sm text-gray-600"
            dangerouslySetInnerHTML={{ __html: job.description || '' }}
          ></p>
        </section>

        {/* Qualification */}
        {/* <section>
          <h2 className="mb-2 text-sm font-semibold text-gray-800">
            Qualification
          </h2>
          <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
            <li>0-2 years of experience in data analysis or a related role.</li>
            <li>
              Solid understanding of statistics, data wrangling, and data
              visualization.
            </li>
            <li>
              Proficiency in Excel and at least one analytics tool (e.g. SQL,
              Python, or R).
            </li>
            <li>
              Experience with data visualization tools like Tableau or Power BI
              is a plus.
            </li>
            <li>Good communication skills and an analytical mindset.</li>
          </ul>
        </section> */}

        {/* Responsibility */}
        {/* <section>
          <h2 className="mb-2 text-sm font-semibold text-gray-800">
            Responsibility
          </h2>
          <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
            <li>
              Collect, clean, and analyze data from multiple sources to identify
              trends and insights.
            </li>
            <li>
              Build and maintain dashboards and regular reports for different
              teams.
            </li>
            <li>
              Assist in designing and conducting A/B testing or other data
              experiments.
            </li>
            <li>
              Collaborate with business and technical teams to define data
              requirements.
            </li>
            <li>
              Present findings and actionable insights in a clear and concise
              way.
            </li>
          </ul>
        </section> */}

        {/* Additional Requirement */}
        <section>
          <h2 className="mb-2 text-sm font-semibold text-gray-800">
            Additional Requirement
          </h2>
          <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
            <li>
              Candidates must complete the simulation provided by{' '}
              {job?.companyName || ''} and achieve a minimum score of 80% to
              proceed to the next stage of the recruitment process.
            </li>
          </ul>
        </section>

        {/* Job Simulation */}
        <div className="flex items-start gap-4">
          <Image
            src="/job/job-simulation.png"
            alt={job?.companyLogoUrl}
            width={297}
            height={181}
            className="rounded-md object-contain"
            loader={({ src }) => src}
          />

          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-800">
              {job.simulation?.name
                ? job.simulation?.name
                : 'Job Simulation: Junior Data Analyst Challenge'}
            </h3>
            <p className="mt-2 text-sm leading-relaxed text-gray-600">
              {job.simulation?.description
                ? job.simulation?.description
                : 'Before moving to the interview stage, you&apos;ll need to complete our Data Analyst Job Simulation – a short interactive task designed to test your practical skills in data cleaning, analysis, visualization, and interpretation. This helps us understand how you think with data and solve problems in a real-world context.'}
            </p>
            <Button
              className="mt-4 bg-primary text-white"
              disabled={!job.simulation?.id || creatingSimulation || !user}
              onClick={handleStartSimulation}
            >
              Start Simulation Now
            </Button>
          </div>
        </div>
      </div>
      {openLoginModal && (
        <SignInModal
          open={openLoginModal}
          onClose={() => setOpenLoginModal(false)}
          role={Role.USER}
          onLoginSuccess={handleLoginSuccess}
        />
      )}
      {applyCVModal && (
        // <ApplyCVModal
        //   open={applyCVModal}
        //   setOpen={setApplyCVModal}
        //   jobId={job?.jobId || ''}
        //   simulationId={job?.simulation?.id || ''}
        //   onApplySuccess={() => {
        //     refetchIsApplied();
        //   }}
        // />
        <CVApplyEnhancedModal
          open={applyCVModal}
          setOpen={setApplyCVModal}
          jobId={job?.jobId || ''}
          simulationId={job?.simulation?.id || ''}
          onApplySuccess={() => {
            refetchIsApplied();
          }}
        />
      )}
    </>
  );
}
