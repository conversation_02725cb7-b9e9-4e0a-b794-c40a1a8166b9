'use client';

import { OrganizationType } from '@/api-requests/organization';
import { orgAtom } from '@/store/organization-atom';
import UserOrgCompanyHomepage from '@/views/org-homepage/org-company-homepage';
import UserOrgPartnerHomepage from '@/views/org-homepage/org-partner-homepage';
import { useAtom } from 'jotai';

export default function UserOrgHomePage() {
  const [org] = useAtom(orgAtom);
  if (!org) return null;

  return org.type === OrganizationType.COMPANY ? (
    <UserOrgCompanyHomepage />
  ) : (
    <UserOrgPartnerHomepage />
  );
}
