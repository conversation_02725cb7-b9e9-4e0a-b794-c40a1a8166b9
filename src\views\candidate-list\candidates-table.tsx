'use client';

import {
  <PERSON>,
  Checkbox,
  Badge,
  <PERSON>ltip,
  <PERSON>ton,
  Loader,
  Avatar,
  Dropdown,
  ActionIcon,
} from 'rizzu<PERSON>';
import MessageIcon from '@/views/icons/message';
import EditSquareIcon from '@/views/icons/edit-square';
import Image from 'next/image';
import Pagination from '../pagination';
import { useState } from 'react';
import { JobCandidate } from '@/api-requests/job-candidate/types';
import { ApiListResponse, LIMIT } from '@/api-requests/types';
import { MoreHorizontalIcon } from 'lucide-react';
import DownloadCvIcon from '../icons/download-cv';
import HeartOutlineIcon from '../icons/heart-outline';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';
import { safeFormatDate } from '@/utils/date';

const badgeClasses: Record<string, string> = {
  pending: 'bg-stone-200 text-stone-800 ring-1 ring-stone-300',
  screening: 'bg-sky-100   text-sky-800   ring-1 ring-sky-200',
  interview: 'bg-indigo-100 text-indigo-800 ring-1 ring-indigo-200',
  offer: 'bg-amber-100 text-amber-900 ring-1 ring-amber-200',
  rejected: 'bg-rose-100  text-rose-800  ring-1 ring-rose-200',
  withdrawn: 'bg-zinc-200  text-zinc-800  ring-1 ring-zinc-300',
  hired: 'bg-emerald-100 text-emerald-800 ring-1 ring-emerald-200',
};

const getApplyModeText = (mode: string) => {
  switch (mode) {
    case 'cv':
      return 'CV';
    case 'simulation':
      return 'Simulation';
    default:
      return '';
  }
};

interface IProps {
  jobCandidateData: ApiListResponse<ShortlistCandidate>;
  isLoading: boolean;
  page: number;
  setPage: (page: number) => void;
  onClickAction: (action: string, candidate: ShortlistCandidate) => void;
  onCheckboxChange: (
    checked: boolean,
    candidate: ShortlistCandidate | string
  ) => void;
  selectedCandidates: ShortlistCandidate[];
}

const LongTextCell = ({
  text,
  lines = 3,
}: {
  text: string;
  lines?: number;
}) => {
  const [expanded, setExpanded] = useState(false);

  const clampStyle: React.CSSProperties = expanded
    ? {}
    : {
        display: '-webkit-box',
        WebkitLineClamp: lines,
        WebkitBoxOrient: 'vertical',
        overflow: 'hidden',
      };

  return (
    <div className="relative max-w-full">
      <span className="text-sm text-gray-800" style={clampStyle}>
        {text}
      </span>

      {!expanded && (
        <div className="pointer-events-none absolute inset-x-0 bottom-6 h-6 bg-gradient-to-t from-white" />
      )}

      <Button
        variant="text"
        onClick={() => setExpanded((v) => !v)}
        className="mt-1 inline-flex !h-auto !w-auto !p-0 text-xs font-medium text-gray-600 hover:underline"
        aria-expanded={expanded}
      >
        {expanded ? 'Show less' : 'Show more'}
      </Button>
    </div>
  );
};

export default function CandidatesTable({
  jobCandidateData,
  isLoading,
  page,
  setPage,
  onClickAction,
  onCheckboxChange,
  selectedCandidates,
}: IProps) {
  const actions = [
    // {
    //   icon: <MessageIcon className="h-5 w-5" />,
    //   label: 'Message',
    // },
    // {
    //   icon: <DownloadCvIcon className="h-5 w-5" />,
    //   label: 'Download CV',
    // },
    {
      icon: <HeartOutlineIcon className="h-5 w-5" />,
      label: 'Add to Shortlist',
    },
    {
      icon: <EditSquareIcon className="h-5 w-5" />,
      label: 'View Detail',
    },
  ];

  return (
    <div>
      <div className="rounded-xl bg-white pb-5 shadow-lg">
        <div className="overflow-x-auto">
          <Table>
            <Table.Header className="rounded-t-xl !bg-[#FAFAFA]">
              <Table.Row>
                <Table.Head className="w-4">
                  <Checkbox
                    variant="flat"
                    size="sm"
                    checked={
                      selectedCandidates.length ===
                        jobCandidateData?.data?.length &&
                      jobCandidateData?.data?.length > 0
                    }
                    onChange={(e) => onCheckboxChange(e.target.checked, 'all')}
                  />
                </Table.Head>
                <Table.Head className="w-[20%]">Candidate name</Table.Head>
                <Table.Head className="w-[100px]">Match</Table.Head>
                <Table.Head className="w-[20%]">Job</Table.Head>
                <Table.Head className="w-[20%]">Status</Table.Head>
                <Table.Head className="w-[20%]">Applied</Table.Head>
                <Table.Head className="w-full">AI Review</Table.Head>
                <Table.Head className="!text-right">Actions</Table.Head>
              </Table.Row>
            </Table.Header>

            <Table.Body>
              {jobCandidateData?.data?.map((candidate, index) => (
                <Table.Row key={index}>
                  <Table.Cell>
                    <Checkbox
                      variant="flat"
                      size="sm"
                      checked={selectedCandidates.some(
                        (c) => c._id === candidate._id
                      )}
                      onChange={(e) =>
                        onCheckboxChange(e.target.checked, candidate)
                      }
                    />
                  </Table.Cell>

                  <Table.Cell>
                    <div
                      className="flex cursor-pointer items-center gap-1"
                      onClick={() => {
                        onClickAction('View Detail', candidate);
                      }}
                    >
                      <Avatar
                        src={
                          candidate.user?.avatar || '/avatar/user-default.png'
                        }
                        name={
                          candidate.user?.firstName +
                          ' ' +
                          candidate.user?.lastName
                        }
                        customSize={50}
                        className="!bg-transparent"
                      />
                      <div className="text-left">
                        <div className="font-medium text-gray-900">
                          {candidate.user?.firstName +
                            ' ' +
                            candidate.user?.lastName}
                        </div>
                        <div className="text-xs text-gray-500">
                          {candidate.email}
                        </div>
                      </div>
                    </div>
                  </Table.Cell>

                  <Table.Cell>
                    <span className="text-sm">
                      {candidate.matchPercentage || 0}%
                    </span>
                  </Table.Cell>

                  <Table.Cell>
                    <span className="text-sm">
                      {candidate.job?.title || '-'}
                    </span>
                  </Table.Cell>

                  <Table.Cell>
                    <div>{candidate.simulation?.name}</div>
                    <Badge
                      variant="flat"
                      size="sm"
                      className={badgeClasses[candidate.applicationStatus]}
                    >
                      {candidate.applicationStatus}
                    </Badge>
                  </Table.Cell>

                  <Table.Cell>
                    <div className="flex flex-col gap-1">
                      <p className="text-sm">
                        <Tooltip
                          color="invert"
                          content={safeFormatDate(candidate.appliedAt, {
                            format: 'full',
                          })}
                        >
                          <span>
                            {safeFormatDate(candidate.appliedAt, {
                              format: 'short',
                            })}
                          </span>
                        </Tooltip>
                      </p>

                      <p className="text-sm text-gray-500">
                        Method: {getApplyModeText(candidate.applyMode)}
                      </p>
                    </div>
                  </Table.Cell>

                  <Table.Cell>
                    <div>
                      <LongTextCell
                        text={candidate.aiEvaluation?.summary || ''}
                        lines={2}
                      />
                    </div>
                    {/* <ul className="list-inside list-disc space-y-1 text-sm text-gray-700">
                    {candidate.review.map((line, i) => (
                      <li key={i}>{line}</li>
                    ))}
                  </ul> */}
                  </Table.Cell>

                  <Table.Cell className="text-right">
                    <div className="flex justify-end gap-3">
                      <Dropdown placement="bottom-end">
                        <Dropdown.Trigger>
                          <MoreHorizontalIcon />
                        </Dropdown.Trigger>
                        <Dropdown.Menu className="w-fit divide-y">
                          {actions.map((action, idx) => (
                            <Dropdown.Item
                              key={idx}
                              className="hover:bg-primary hover:text-white"
                              onClick={() =>
                                onClickAction(action.label, candidate)
                              }
                            >
                              <div className="flex items-center">
                                {action.icon}
                                <span className="ml-2">{action.label}</span>
                              </div>
                            </Dropdown.Item>
                          ))}
                        </Dropdown.Menu>
                      </Dropdown>
                    </div>
                  </Table.Cell>
                </Table.Row>
              ))}

              {isLoading ? (
                <Table.Row>
                  <Table.Cell colSpan={7} className="h-40 text-center">
                    <div className="flex min-h-40 items-center justify-center">
                      <Loader className="h-8 w-8" />
                    </div>
                  </Table.Cell>
                </Table.Row>
              ) : (
                jobCandidateData?.data?.length === 0 && (
                  <Table.Row>
                    <Table.Cell colSpan={7} className="text-center">
                      <div className="text-gray-500">No candidates found</div>
                    </Table.Cell>
                  </Table.Row>
                )
              )}
            </Table.Body>
          </Table>
        </div>

        <hr className="border-t border-gray-100 pt-4" />

        <Pagination
          total={jobCandidateData?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>
    </div>
  );
}
