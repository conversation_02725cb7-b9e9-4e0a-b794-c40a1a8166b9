import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ShortlistQueryKeys, CreateParams, Shortlist } from './types';

export async function createShortlist(
  payload: CreateParams
): Promise<Shortlist> {
  const reps = await axiosInstance.post<Shortlist>(
    API_ENDPONTS.CREATE_SHORTLIST,
    payload
  );
  return reps.data;
}

export const useCreateShortlist = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateParams) => createShortlist(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShortlistQueryKeys.CREATE_SHORTLIST],
      });
    },
  });
};
