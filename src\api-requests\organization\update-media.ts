import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { Organization } from './types';

export async function updateMedia(
  id: string,
  payload: { file: File; type: 'logo' | 'banner' }
): Promise<Organization> {
  const reps = await axiosInstance.patch<Organization>(
    API_ENDPONTS.UPDATE_MEDIA.replace(':id', id),
    payload,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return reps.data;
}
