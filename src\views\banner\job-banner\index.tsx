'use client';

import dynamic from 'next/dynamic';
import HeroTrial from './hero-trial';
import { usePathname } from 'next/navigation';
import JobSuggestion from './job-suggestion';

const JobSearch = dynamic(() => import('./job-search'), {
  ssr: false,
});
const JobFilter = dynamic(() => import('./job-filter'), {
  ssr: false,
});

export default function JobBanner() {
  const pathname = usePathname();
  const isHomePage = pathname === '/';

  return (
    <>
      {isHomePage && (
        <section
          className="relative bg-cover bg-center bg-no-repeat px-4 py-14 xl:px-0 xl:py-24"
          style={{ backgroundImage: 'url("/job/fast-feedback.png")' }}
        >
          <div className="mx-auto flex max-w-[1440px] flex-col gap-6">
            <HeroTrial />
          </div>
        </section>
      )}

      <div
        className="relative w-full bg-cover bg-bottom bg-no-repeat py-14 xl:py-24"
        style={{
          backgroundImage: `url("/job/job-hero.jpg")`,
        }}
      >
        <div className="mx-auto flex max-w-[1440px] flex-col gap-6 px-4 xl:px-0">
          {/* <div>
              <h1 className="mb-2 text-3xl font-bold text-primary md:text-4xl">
                Try the Job Before You Apply
              </h1>
              <p className="mb mx-auto text-base text-gray-600 md:text-lg">
                No more ghosted applications. Land better jobs through job
                simulations.
              </p>
            </div> */}

          <div className="space-y-4 mb-8">
            <JobSearch />

            <JobFilter isHomePage={isHomePage} />
          </div>

          {isHomePage && <JobSuggestion />}
        </div>
      </div>
    </>
  );
}
