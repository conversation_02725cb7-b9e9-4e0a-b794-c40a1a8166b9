import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { JobCandidateQueryKeys, OrgCandidateAdminListParams } from './types';
import { cleanQueryParams } from '@/utils/url';
import { ApiListResponse } from '../types';
import { ShortlistCandidate } from '../shortlist-candidate';

export async function getCandidatesForAdmin(
  params: OrgCandidateAdminListParams
): Promise<ApiListResponse<ShortlistCandidate>> {
  const reps = await axiosInstance.get<ApiListResponse<ShortlistCandidate>>(
    API_ENDPONTS.GET_CANDIDATES_FOR_ADMIN,
    {
      params: cleanQueryParams(params),
    }
  );
  return reps.data;
}

export function useGetCandidatesForAdmin(params: OrgCandidateAdminListParams) {
  return useQuery<ApiListResponse<ShortlistCandidate>>({
    queryKey: [JobCandidateQueryKeys.GET_CANDIDATE_FOR_ADMIN, params],
    queryFn: () => getCandidatesForAdmin(params),
    enabled: true,
  });
}
