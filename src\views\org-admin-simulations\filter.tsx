'use client';

import SearchIcon from '@/views/icons/search';
import { Input, InputProps, SelectOption, type SelectProps } from 'rizzui';
import { Option } from './index';

interface IProps {
  searchProps: InputProps & { ref?: React.RefObject<HTMLInputElement> };
  statusProps?: {
    onClear: () => void;
    onChange: (option: Option | null) => void;
  } & Omit<SelectProps<SelectOption>, 'options' | 'onChange'>;
}

export default function OrgAdminSimulationsFilter({ searchProps }: IProps) {
  return (
    <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
      <div className="w-full sm:w-auto">
        <Input
          variant="flat"
          prefix={<SearchIcon className="h-6 w-6 text-gray-400" />}
          placeholder="Search..."
          className="w-full min-w-[200px] rounded-lg bg-white px-2 shadow-sm [&_*]:!border-0 [&_*]:!border-none [&_*]:!shadow-none [&_*]:!outline-none [&_*]:!ring-0"
          inputClassName="!border-0 !shadow-none !outline-none !ring-0 focus:!ring-0 p-0 text-sm"
          size="md"
          {...searchProps}
        />
      </div>

      {/* <div className="flex flex-wrap items-center gap-3">
        <div className="whitespace-nowrap text-sm opacity-50">Sort by:</div>

        <div className="flex flex-wrap gap-3">
          <Select
            clearable
            options={matchOptions}
            placeholder="Select status"
            className="w-full min-w-[130px] rounded-lg shadow-[0_1px_3px_0_rgba(0,0,0,0.08)] transition-all duration-300 ease-in-out hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.15)] sm:w-auto [&>.rizzui-popover]:rounded-xl [&>.rizzui-popover]:shadow-md [&_.rizzui-select-button]:!border [&_.rizzui-select-button]:!border-gray-200"
            size="sm"
            {...statusProps}
          />
        </div>
      </div> */}
    </div>
  );
}
