import { <PERSON><PERSON>, Button } from 'rizzui';
import { ProfileInfo } from '../profile/user/profile-detail';
import { JobCandidate } from '@/api-requests/job-candidate/types';

const getMonthName = (humanMonth: number) => {
  return new Date(new Date().getFullYear(), humanMonth - 1).toLocaleString(
    'en-US',
    {
      month: 'short',
    }
  );
};

interface IProps {
  candidate: JobCandidate;
}

export default function ProfileTab({ candidate }: IProps) {
  const cvData = candidate?.cvData;
  const strengths = candidate.aiEvaluation?.cvProcessResult?.result?.strengths;
  const weaknesses =
    candidate.aiEvaluation?.cvProcessResult?.result?.areasForImprovement;

  const hasAIOverview = strengths?.length || weaknesses?.length;

  if (!cvData) return <div className="p-4">No profile found</div>;
  return (
    <div className="space-y-6">
      <div className="space-y-4 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
        {/* <div className="flex items-center gap-4">
          <Avatar
            src={'/avatar/user-default.png'}
            name={'Courtney Henry'}
            customSize={116}
          />
          <div className="font-bold">Courtney Henry</div>
        </div>

        <hr className="text-[#0D1321]" /> */}

        <div>
          <div className="mb-2 font-bold">About</div>
          <div className="whitespace-pre-line text-sm">{cvData?.about}</div>
        </div>

        <hr className="text-[#0D1321]" />

        <div className="space-y-2">
          <h3 className="flex items-center gap-2 font-bold">
            ✨ AI CV Preview
          </h3>
          {hasAIOverview && (
            <>
              {/* Strengths */}
              {!!strengths?.length && (
                <section>
                  <h4 className="flex items-center gap-2 font-semibold">
                    Strengths
                  </h4>
                  <ul className="mt-2 list-disc pl-6 text-sm">
                    {strengths.map((item, idx) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                </section>
              )}

              {/* Weaknesses */}
              {!!weaknesses?.length && (
                <section>
                  <h4 className="flex items-center gap-2 font-semibold">
                    Weaknesses
                  </h4>
                  <ul className="mt-2 list-disc pl-6 text-sm">
                    {weaknesses.map((item, idx) => (
                      <li key={idx}>{item}</li>
                    ))}
                  </ul>
                </section>
              )}
            </>
          )}
        </div>
      </div>

      {/* <div className="space-y-4 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
        <div className="font-bold">Personal details</div>
        <div className="mt-4 grid grid-cols-1 gap-3 sm:grid-cols-2">
          <ProfileInfo label={'Full name'} value={'Brooklyn Simmons'} />
          <ProfileInfo
            label={'Address'}
            value={'1234 Elm Street, Los Angeles, CA'}
          />
          <ProfileInfo label={'Phone Number'} value={'(*************'} />
          <ProfileInfo label={'Email'} value={'<EMAIL>'} />
          <ProfileInfo
            label={'Website'}
            value={'https://portal.industryconnect.io/en/shop'}
          />
          <ProfileInfo
            label={'Linkedin'}
            value={'https://www.linkedin.com/company/mvp-studio/'}
          />
        </div>
      </div> */}

      <div className="space-y-4 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
        <div className="font-bold">Work experience</div>
        {!!cvData?.experiences?.length && (
          <ul role="list" className="divide-y divide-gray-200">
            {cvData.experiences.map((exp, idx) => (
              <li key={exp.id || idx} className="py-2">
                <div className="grid w-full grid-cols-1 gap-4 py-2 sm:grid-cols-[180px_minmax(0,1fr)]">
                  <div className="text-sm text-gray-500">
                    {exp.startMonth && getMonthName(Number(exp.startMonth))}{' '}
                    {exp.startYear}{' '}
                    {exp.endYear
                      ? `- ${exp.endMonth && getMonthName(Number(exp.endMonth))}
                                          ${exp.endYear}`
                      : '- Present'}
                  </div>

                  <div className="w-full">
                    <div className="flex w-full flex-col">
                      <div className="font-semibold">{exp.title}</div>
                      <p>{exp.company}</p>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="space-y-4 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
        <div className="font-bold">Skills</div>
        {!!cvData.skills?.length && (
          <ul role="list" className="divide-y divide-gray-200">
            {cvData.skills.map((skill, idx) => (
              <li className="py-2" key={skill.id || idx}>
                {skill.skill}
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="space-y-4 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)]">
        <div className="font-bold">Education</div>
        {!!cvData?.educations?.length && (
          <ul role="list" className="divide-y divide-gray-200">
            {cvData?.educations.map((education, idx) => (
              <li className="py-2" key={education.id || idx}>
                <div className="grid w-full grid-cols-1 gap-4 py-2 sm:grid-cols-[180px_minmax(0,1fr)]">
                  <div className="text-sm text-gray-500">
                    {education.startMonth &&
                      getMonthName(Number(education.startMonth))}{' '}
                    {education.startYear}{' '}
                    {education.endYear
                      ? `- ${education.endMonth ? getMonthName(Number(education.endMonth)) : ''}
                        ${education.endYear}`
                      : ''}
                  </div>

                  <div className="w-full">
                    <div className="flex w-full flex-row justify-between gap-2">
                      <div>
                        <p className="font-semibold">{education.school}</p>
                        <p className="text-sm text-[#484848]">
                          {[education.degree, education.field, education.grade]
                            .filter(Boolean)
                            .join(', ')}
                        </p>
                      </div>
                    </div>

                    <p className="my-2 whitespace-pre-line text-sm text-[#0D1321]">
                      {education.description}
                    </p>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="flex justify-end gap-4">
        <Button variant="outline" className="border-primary text-primary">
          Share link CV
        </Button>
        <Button className="bg-primary text-white">Export CV to PDF</Button>
      </div>
    </div>
  );
}
