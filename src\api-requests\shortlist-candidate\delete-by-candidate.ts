import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ShortlistCandidateQueryKeys,
  ShortlistCandidate,
  CreateParams,
} from './types';

export async function deleteShortListCandidate(
  payload: CreateParams
): Promise<ShortlistCandidate> {
  const reps = await axiosInstance.delete<ShortlistCandidate>(
    API_ENDPONTS.DELETE_SHORTLIST_CANDIDATE_BY_CANDIDATE.replace(
      ':candidateId',
      payload.candidateId +
        `?orgId=${payload.orgId}&shortlistId=${payload.shortlistId}`
    )
  );
  return reps.data;
}

export const useDeleteShortListCandidate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateParams) => deleteShortListCandidate(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShortlistCandidateQueryKeys.DELETE_SHORTLIST_CANDIDATE],
      });
    },
  });
};
