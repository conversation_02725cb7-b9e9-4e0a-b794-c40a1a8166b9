'use client';

import Image from 'next/image';
import ClockIcon from '@/views/icons/clock';
import { getJobTypeString } from '@/views/job/job-list';
import { Job } from '@/api-requests/job';
import cn from '@/utils/class-names';
import { useRouter } from 'next/navigation';
import StarsIcon from '@/views/icons/stars';
import LocationIcon from '@/views/icons/location';

interface JobCardProps {
  job: Job;
}

export default function JobCard({ job }: JobCardProps) {
  const router = useRouter();

  const jobType = getJobTypeString(job);

  return (
    <div
      className={cn(
        'flex h-full flex-col justify-between rounded-[16px] bg-white p-6 transition-all duration-300 hover:scale-[1.01] hover:shadow-[0_4px_30px_rgba(0,0,0,0.20)]'
      )}
    >
      <div className="space-y-4">
        <div className="flex items-center justify-between gap-2">
          <div className="flex flex-1 items-center gap-2">
            {job?.companyLogoUrl ? (
              <Image
                src={job.companyLogoUrl}
                alt={job.companyName || 'Company Logo'}
                width={32}
                height={32}
                className="h-8 w-8 rounded-full object-contain"
                loader={({ src }) => src}
              />
            ) : (
              <div
                className="!h-8 !w-8 rounded-full bg-gray-100"
                style={{ width: '32px', height: '32px' }}
              />
            )}
            <div className="line-clamp-1">{job?.companyName}</div>
          </div>
          <button className="flex items-center gap-1 rounded-full border border-[#05F98F] bg-[#EDFFF1] px-3 py-1 text-sm hover:bg-[#CCFFE7]">
            <StarsIcon className="h-4 w-4 text-[#A36EFF]" />
            <span className="text-[#00BF50]">Try now</span>
          </button>
        </div>

        {/* Title + Badge */}
        <div>
          <div className="text-md line-clamp-2 flex-1 font-semibold text-gray-800">
            {job?.title}
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-[12px] text-gray-500">
              <LocationIcon className="h-3 w-3" />
              <span>{job?.location}</span>
            </div>
            <div className="flex items-center gap-1 text-[12px] text-gray-500">
              <ClockIcon className="h-3 w-3" />
              <span>{jobType || '-'}</span>
            </div>
          </div>
        </div>

        <div>
          {/* 3 columns */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            {/* Estimated time */}
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <div className="text-sm text-slate-400">
                  Estimated Time (min)
                </div>
                <div className="mt-1 font-bold">
                  {job?.simulation?.minute || '-'}
                </div>
              </div>

              {/* Past outcomes */}
              <div>
                <div className="text-sm text-slate-400">Past Outcomes Time</div>
                <div className="mt-1 font-bold">58% interviewed</div>
              </div>
            </div>

            {/* Predicted score + progress */}
            <div className="min-w-0">
              <div className="text-sm text-slate-400">Predicted Score</div>
              <div className="mt-1 flex items-center gap-4">
                <div className="shrink-0 font-bold">
                  {72}&ndash;{84}%
                </div>
                <div className="relative h-2 w-full rounded-full bg-slate-200">
                  <div
                    className="absolute inset-y-0 left-0 rounded-full bg-gradient-to-r from-indigo-500 via-fuchsia-500 to-cyan-400"
                    style={{ width: `${84}%` }}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="mt-4 flex min-h-7 flex-wrap items-center gap-3">
            {job?.skills?.map((t) => (
              <span
                key={t}
                className="bg-[#748CAB]/9 inline-flex items-center rounded-full border border-[#748CAB] px-2 py-1 text-[12px] text-[#748CAB]"
              >
                {t}
              </span>
            ))}
          </div>
        </div>

        <div className="mt-5 flex items-center justify-between">
          <button className="rounded-full bg-primary px-4 py-2 text-sm font-medium text-white hover:bg-primary/80">
            Try it now
          </button>
          <button
            className="ml-4 text-sm font-medium text-gray-600 underline"
            onClick={(e) => {
              e.stopPropagation();
              router.push(`/find-jobs?id=${job.jobId}`);
            }}
          >
            View detail
          </button>
        </div>
      </div>
    </div>
  );
}
