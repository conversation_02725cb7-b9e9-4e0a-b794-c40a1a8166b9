const QuickAnalyticCardSkeleton = () => {
  return (
    <div className="flex min-w-[240px] flex-1 flex-col justify-between gap-4 rounded-xl bg-white p-4 shadow-md">
      <div className="h-5 w-1/2 animate-pulse rounded bg-gray-200" />
      <div className="h-6 w-1/3 animate-pulse rounded bg-gray-200" />
    </div>
  );
};

export const CandidatesQuickAnalyticsSkeleton = ({
  count = 4,
}: {
  count?: number;
}) => {
  return (
    <div className="flex w-full flex-row flex-wrap gap-1 md:gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <QuickAnalyticCardSkeleton key={i} />
      ))}
    </div>
  );
};
