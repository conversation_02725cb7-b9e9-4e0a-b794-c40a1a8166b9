import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { JobQueryKeys, Job } from './types';

export async function processJobSimulation(params: {
  jobId: string;
}): Promise<Job> {
  const reps = await axiosInstance.patch<Job>(
    API_ENDPONTS.JOB_PROCESS_SIMULATION.replace(':jobId', params.jobId),
    {
      ...params,
    }
  );
  return reps.data;
}

export const useProcessJobSimulation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: { jobId: string }) => processJobSimulation(body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.PROCESS_JOB_SIMULATION],
      });
    },
  });
};
