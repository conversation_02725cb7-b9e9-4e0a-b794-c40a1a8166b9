'use client';

import { Ava<PERSON>, Checkbox } from 'rizzui';
import { useState } from 'react';
import Image from 'next/image';
import Pagination from '../pagination';
import { User } from '@/api-requests/user';
import { ApiListResponse, LIMIT } from '@/api-requests/types';
import {
  formatExperience,
  formatSalary,
  formatWorkType,
} from '../profile/user/profile-detail';
import CVDetailModal from './cv-detail-modal';

interface IProps {
  page: number;
  setPage: (page: number) => void;
  talent: ApiListResponse<User>;
  isLoading?: boolean;
}

export default function CandidateList({
  page,
  setPage,
  talent,
  isLoading,
}: IProps) {
  const [openCvDetail, setOpenCvDetail] = useState(false);
  const [talentSelected, setTalentSelected] = useState<User | null>(null);

  return (
    <>
      <div className="col-span-12 space-y-4 md:col-span-8 lg:col-span-9">
        <div className="space-y-4">
          {isLoading ? (
            [...Array(3)].map((_, i) => (
              <div
                key={i}
                className="flex h-[333px] animate-pulse items-start gap-4 rounded-lg border p-4"
              >
                <div className="h-10 w-10 rounded-full bg-gray-300"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-1/3 rounded bg-gray-300"></div>
                  <div className="h-3 w-1/4 rounded bg-gray-200"></div>
                  <div className="mt-3 space-y-2">
                    <div className="flex gap-4">
                      <div className="h-3 w-20 rounded bg-gray-200"></div>
                      <div className="h-3 w-32 rounded bg-gray-300"></div>
                    </div>
                    <div className="flex gap-4">
                      <div className="h-3 w-16 rounded bg-gray-200"></div>
                      <div className="h-3 w-40 rounded bg-gray-300"></div>
                    </div>
                    <div className="flex gap-4">
                      <div className="h-3 w-20 rounded bg-gray-200"></div>
                      <div className="h-3 w-28 rounded bg-gray-300"></div>
                    </div>
                  </div>
                  <div className="mt-3 h-8 w-20 rounded bg-gray-200"></div>
                </div>
              </div>
            ))
          ) : talent?.data?.length > 0 ? (
            talent?.data.map((t, i) => (
              <div
                key={i}
                className="flex items-start justify-between rounded-lg border p-4 shadow-lg transition-shadow duration-300 hover:shadow-xl md:p-6"
              >
                <div className="flex gap-4">
                  {/* <div>
                <Checkbox variant="flat" size="sm" />
              </div> */}
                  <div>
                    <div className="flex items-center gap-3">
                      <Avatar
                        src={t.avatar}
                        name={t.fullName || '/avatar/user-default.png'}
                        customSize={40}
                      />
                      <div>
                        <h4 className="text-md font-bold">
                          {t.firstName} {t.lastName}
                        </h4>
                        <p className="text-[12px]">{t.role}</p>
                      </div>
                    </div>
                    <div className="mt-3">
                      <div className="grid grid-cols-[140px_1fr] gap-y-1 text-sm">
                        <div className="text-gray-400">Location</div>
                        <div className="text-gray-800">
                          {t?.profile?.location || '-'}
                        </div>

                        <div className="text-gray-400">Skill</div>
                        <div className="text-gray-800">
                          {t.profile?.skills?.join(', ') || '-'}
                        </div>

                        <div className="text-gray-400">Industry</div>
                        <div className="text-gray-800">
                          {t.profile?.industries?.join(', ') || '-'}
                        </div>

                        <div className="text-gray-400">Level</div>
                        <div className="text-gray-800">
                          {t.profile?.levels?.join(', ') || '-'}
                        </div>

                        <div className="text-gray-400">Work type</div>
                        <div className="text-gray-800">
                          {formatWorkType(t.profile?.workTypes)}
                        </div>

                        <div className="text-gray-400">Work place</div>
                        <div className="text-gray-800">
                          {t.profile?.workPlaces?.join(', ') || '-'}
                        </div>

                        <div className="text-gray-400">Expected salary</div>
                        <div className="text-gray-800">
                          {formatSalary(t.profile?.expectedSalary)}
                        </div>

                        <div className="text-gray-400">Experience</div>
                        <div className="text-gray-800">
                          {formatExperience(t?.profile?.experience)}
                        </div>
                      </div>
                      <div className="mt-3 flex gap-4">
                        <button
                          className="rounded border border-primary px-3 py-1 text-sm text-black hover:bg-primary hover:text-white"
                          onClick={() => {
                            setTalentSelected(t);
                            setOpenCvDetail(true);
                          }}
                        >
                          View CV
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                {/* <div className="flex flex-col items-center rounded-md bg-primary px-2 py-0.5 text-white">
              <div className="-mb-1 font-bold">{t.match}%</div>
              <div className="text-[10px]">Match</div>
            </div> */}
              </div>
            ))
          ) : (
            <div className="flex h-[333px] items-start justify-between rounded-lg border p-4 shadow-lg transition-shadow duration-300 hover:shadow-xl md:p-6">
              <div className="m-auto text-gray-400">No candidates found</div>
            </div>
          )}
        </div>

        <div>
          <Pagination
            total={talent?.meta?.total || 0}
            current={page}
            pageSize={LIMIT}
            defaultCurrent={1}
            showLessItems={true}
            onChange={(page: number) => setPage(page)}
            prevIconClassName="py-0 text-gray-500 !leading-[26px]"
            nextIconClassName="py-0 text-gray-500 !leading-[26px]"
            variant="solid"
            className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
            // disabled={isLoading}
          />
        </div>
      </div>

      {openCvDetail && (
        <CVDetailModal
          open={openCvDetail}
          onClose={() => setOpenCvDetail(false)}
          talent={talentSelected as User}
        />
      )}
    </>
  );
}
