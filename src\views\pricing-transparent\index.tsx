'use client';

import Image from 'next/image';
import { Button } from 'rizzui';
import cn from '@/utils/class-names';

export default function PricingTransparent() {
  const plans = [
    {
      name: 'STARTER',
      icon: '/pricing/starter.png',
      price: '$49',
      subtitle: 'per job posting',
      features: [
        '<strong>30</strong> day job listing',
        'Up to <strong>50</strong> applications',
        'Basic simulation analytics',
        'Email support',
        'Standard job promotion',
      ],
      ctaStyle: 'border border-primary text-primary bg-white',
      variant: 'outline' as 'outline' | 'flat' | 'solid' | 'text',
    },
    {
      name: 'PROFESSIONAL',
      mostPopular: true,
      icon: '/pricing/professional.png',
      price: '$89',
      subtitle: 'per job posting',
      features: [
        '<strong>60</strong> day job listing',
        'Unlimited applications',
        'Advanced analytics & insights',
        'Priority support',
        'Featured job promotion',
        'Candidate comparison tools',
      ],
      ctaStyle: 'bg-primary text-white',
      variant: 'solid' as 'outline' | 'flat' | 'solid' | 'text',
    },
    {
      name: 'ENTERPRISE',
      icon: '/pricing/enterprise.png',
      price: 'Custom',
      subtitle: 'for high-volume hiring',
      features: [
        '<strong>Unlimited</strong> job postings',
        'Custom simulation design',
        'Dedicated account manager',
        'API access',
        'White-table options',
        'Custom integrations',
      ],
      ctaStyle: 'border border-primary text-primary bg-white',
      variant: 'outline' as 'outline' | 'flat' | 'solid' | 'text',
    },
  ];

  return (
    <div className="mx-auto max-w-[1200px] bg-white px-4 xl:px-0">
      {/* Header + Stats */}
      <div className="mb-12 flex flex-col gap-8 lg:flex-row lg:items-center lg:justify-between">
        {/* Header */}
        <div className="flex-1 text-center lg:text-left">
          <h2 className="text-2xl font-bold text-gray-800 md:text-3xl">
            Simple, Transparent Pricing
          </h2>
          <p className="mt-2 text-gray-500">
            Choose the plan that fits your hiring needs.
            <br className="hidden sm:inline" />
            No hidden fees, no long term contracts.
          </p>
        </div>

        {/* Stats */}
        <div className="flex w-full items-center justify-between rounded-xl border border-primary px-4 py-4 shadow-sm lg:w-auto">
          {[
            { label: 'Application Today', value: '24' },
            { label: 'Total Simulations', value: '156' },
            { label: 'Completion Rate', value: '92%' },
            { label: 'Avg.Score', value: '8.7' },
          ].map((item, index, arr) => (
            <div
              key={index}
              className={`relative flex flex-col items-center px-6`}
            >
              <div className="text-3xl font-bold text-gray-800">
                {item.value}
              </div>
              <div className="text-sm text-gray-500">{item.label}</div>
              {index !== arr.length - 1 && (
                <div className="absolute bottom-2 right-0 top-2 w-px bg-gray-300 opacity-70"></div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
        {plans.map((plan) => (
          <div
            key={plan.name}
            className={cn(
              'relative rounded-2xl border p-6 shadow transition duration-300 hover:shadow-lg hover:shadow-gray-300/70',
              plan.mostPopular ? 'border-primary border-2' : ''
            )}
          >
            {plan.mostPopular && (
              <>
                <div className="bg-primary absolute right-2 top-2 rounded-full px-2 py-0.5 text-[10px] font-bold text-white">
                  Most Popular
                </div>
                <div className="bg-primary absolute left-[50%] top-0 h-2 w-[40%] translate-x-[-50%] rounded-b-md"></div>
                <div className="bg-primary absolute bottom-0 left-[50%] h-2 w-[80%] translate-x-[-50%] rounded-t-md"></div>
              </>
            )}

            <div className="mb-4 flex items-center gap-4">
              <div>
                <Image
                  src={plan.icon}
                  alt={`${plan.name}`}
                  width={100}
                  height={100}
                  className="object-contain"
                  loader={({ src }) => src}
                />
              </div>
              <div className="flex flex-col items-start gap-3">
                <h3 className="text-sm font-medium text-gray-700">
                  {plan.name}
                </h3>
                <div className="bg-primary mt-1 h-[2px] w-6"></div>
                <div>
                  <div className="text-3xl font-bold text-gray-900">
                    {plan.price}
                  </div>
                  <div className="mb-6 text-sm text-gray-500">
                    {plan.subtitle}
                  </div>
                </div>
              </div>
            </div>

            <Button
              className={`mb-6 w-full rounded-md py-2 text-sm font-medium ${plan.ctaStyle}`}
              variant={plan.variant}
            >
              Get Started
            </Button>

            <ul className="space-y-2 text-sm text-gray-700">
              {plan.features.map((feat, i) => (
                <li key={i} className="flex items-start gap-2">
                  <span className="mt-0.5 text-primary">✔</span>
                  <span dangerouslySetInnerHTML={{ __html: feat }} />
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
}
