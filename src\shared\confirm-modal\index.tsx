'use client';

import CloseIcon from '@/views/icons/close';
import { ActionIcon, Button, Modal, Title } from 'rizzui';

interface IProps {
  title: string;
  content: string | React.ReactNode;
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  isLoading?: boolean;
  confirmButtonText?: string;
}

export default function ConfirmModal({
  title,
  open,
  content,
  onClose,
  onConfirm,
  isLoading,
  confirmButtonText,
}: IProps) {
  return (
    <Modal isOpen={open} onClose={onClose} size="lg" customSize={'450px'}>
      <div className="w-full rounded-[20px] p-6 sm:w-[450px]">
        <div className="mb-5 flex items-center justify-between">
          <Title as="h4">{title}</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-6">
          <div>{content}</div>

          <div className="mt-6 flex justify-end space-x-4">
            <Button
              variant="outline"
              className="border-primary text-primary hover:bg-primary hover:text-white"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              className="bg-primary text-white"
              onClick={onConfirm}
              isLoading={isLoading}
              disabled={isLoading}
            >
              {confirmButtonText || 'Save'}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
