import React from 'react';

function UnpublishedIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      {...props}
    >
      <path
        fill="currentColor"
        d="M12 20q1.125 0 2.138-.3t1.912-.825L12.175 15l-.875.875q-.3.3-.712.3t-.713-.3L7.05 13.05q-.275-.275-.275-.7t.275-.7t.7-.275t.7.275l2.15 2.15l.175-.2l-5.65-5.65q-.525.9-.825 1.913T4 12q0 3.325 2.338 5.663T12 20m3.05-7.85l-1.4-1.4l1.9-1.9q.275-.275.7-.275t.7.275t.275.7t-.275.7zm4.025 9.75L17.5 20.35q-1.225.8-2.612 1.225T12 22q-2.075 0-3.9-.788t-3.175-2.137T2.788 15.9T2 12q0-1.5.425-2.887T3.65 6.5L2.075 4.925q-.3-.3-.3-.712t.3-.713t.713-.3t.712.3l17 17q.3.3.3.7t-.3.7t-.712.3t-.713-.3M12 4q-.825 0-1.612.163T8.85 4.65q-.4.175-.8.05t-.6-.475t-.088-.75t.488-.575q.975-.45 2.012-.675T12 2q2.075 0 3.9.788t3.175 2.137T21.213 8.1T22 12q0 1.1-.225 2.138T21.1 16.15q-.175.375-.575.488t-.75-.088t-.475-.6t.05-.8q.325-.75.488-1.537T20 12q0-3.325-2.337-5.663T12 4m-1.425 9.425"
      />
    </svg>
  );
}

export default UnpublishedIcon;
