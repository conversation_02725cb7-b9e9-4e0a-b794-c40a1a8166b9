'use client';

import {
  ProcessCVToApplyResponse,
  useApplyCV,
  useProcessCVToApply,
} from '@/api-requests/job/process-cv-to-apply';
import SimpleMarkdown from '@/components/SimpleMarkdown';
import StartSimulationButton from '@/components/StartSimulationButton';
import { userAtom } from '@/store/user-atom';
import { getUserError } from '@/utils/api-error';
import cn from '@/utils/class-names';
import { useAtom } from 'jotai';
import {
  ChevronLeft,
  ChevronRight,
  Sparkles,
  Upload,
  User,
} from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { Button, Modal, Progressbar } from 'rizzui';

const DELAY_PROGRESS = 1700;

interface IProps {
  jobId: string;
  simulationId: string;
  open: boolean;
  setOpen: (open: boolean) => void;
  onApplySuccess: () => void;
  onErrorJobApplied?: () => void;
}

interface CVSelectionData {
  type: 'upload' | 'profile';
  file?: File;
}

const CVSelectionStep = ({
  onNext,
  onClose,
}: {
  onNext: (data: CVSelectionData) => void;
  onClose: () => void;
}) => {
  const [user] = useAtom(userAtom);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const validateChoosenFile = (file?: File) => {
    if (!file) {
      toast.error('Please select a CV file');
      return false;
    }
    // Check file type
    const allowedTypes = [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ];
    if (!allowedTypes.includes(file.type)) {
      toast.error('Please select a PDF or DOCX file');
      return false;
    }

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      toast.error('File size must be less than 2MB');
      return false;
    }
    return true;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (validateChoosenFile(file)) {
      setSelectedFile(file!);
    }
    // if (file) {
    //   // Check file type
    //   const allowedTypes = [
    //     'application/pdf',
    //     'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    //   ];
    //   if (!allowedTypes.includes(file.type)) {
    //     toast.error('Please select a PDF or DOCX file');
    //     return;
    //   }

    //   // Check file size (max 2MB)
    //   if (file.size > 2 * 1024 * 1024) {
    //     toast.error('File size must be less than 2MB');
    //     return;
    //   }

    //   setSelectedFile(file);
    // }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const file = e.dataTransfer.files?.[0];
    if (validateChoosenFile(file)) {
      setSelectedFile(file);
    }
    // if (file) {
    //   // Check file type
    //   const allowedTypes = [
    //     'application/pdf',
    //     'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    //   ];
    //   if (!allowedTypes.includes(file.type)) {
    //     toast.error('Please select a PDF or DOCX file');
    //     return;
    //   }

    //   // Check file size (max 2MB)
    //   if (file.size > 2 * 1024 * 1024) {
    //     toast.error('File size must be less than 2MB');
    //     return;
    //   }

    //   setSelectedFile(file);
    // }
  };

  const handleUploadCV = () => {
    if (!selectedFile) {
      toast.error('Please select a CV file');
      return;
    }
    onNext({ type: 'upload', file: selectedFile });
  };

  const handleUseProfileCV = () => {
    if (!user?.profile?.cv) {
      toast.error('No CV found in your profile');
      return;
    }
    onNext({ type: 'profile' });
  };

  return (
    <div className="flex flex-col items-center gap-6 p-8 text-center">
      <img src="/job/cv-edit.png" alt="" className="h-auto w-36" />
      <div>
        <p className="mb-2 text-2xl font-bold text-[#484848]">Choose Your CV</p>
        <p className="text-[16px] text-gray-600">
          Select how you want to apply for this position
        </p>
      </div>

      <div className="flex w-full flex-row flex-wrap gap-3">
        {/* Upload CV Option */}
        <div className="min-w-[300px] flex-1 rounded-lg border-2 border-dashed border-gray-300 p-6 transition-colors hover:border-primary">
          <div
            className={cn(
              'cursor-pointer',
              dragActive && 'bg-primary/5 border-primary'
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <input
              type="file"
              accept=".pdf,.docx"
              onChange={handleFileSelect}
              className="hidden"
              id="cv-upload"
            />
            <label htmlFor="cv-upload" className="cursor-pointer">
              <div className="flex flex-col items-center gap-3">
                <Upload className="h-8 w-8 text-gray-400" />
                <div>
                  <p className="font-medium text-gray-700">Upload New CV</p>
                  <p className="text-sm text-gray-500">
                    {selectedFile ? selectedFile.name : 'PDF or DOCX, max 10MB'}
                  </p>
                </div>
              </div>
            </label>
          </div>
          {selectedFile && (
            <Button
              className="mt-4 w-full bg-primary text-white"
              onClick={handleUploadCV}
            >
              Continue with Uploaded CV
            </Button>
          )}
        </div>

        {/* Use Profile CV Option */}
        {user?.profile?.cv && (
          <div className="min-w-[300px] flex-1 rounded-lg border-2 border-gray-300 p-6 transition-colors hover:border-primary">
            <div className="flex flex-col items-center gap-3">
              <User className="h-8 w-8 text-gray-400" />
              <div>
                <p className="font-medium text-gray-700">Use Profile CV</p>
                <p className="text-sm text-gray-500">
                  Use the CV from your profile
                </p>
              </div>
            </div>
            <Button
              className="mt-4 w-full bg-primary text-white"
              onClick={handleUseProfileCV}
            >
              Continue with Profile CV
            </Button>
          </div>
        )}
      </div>

      <Button variant="outline" onClick={onClose} className="mt-4">
        Cancel
      </Button>
    </div>
  );
};

const ApplyCVProcessingCVStep = ({
  jobId,
  simulationId,
  cvData,
  onSuccess,
  onErrorJobApplied,
}: {
  jobId: string;
  simulationId: string;
  cvData: CVSelectionData;
  onSuccess: (result: ProcessCVToApplyResponse | null) => void;
  onErrorJobApplied?: () => void;
}) => {
  const [progress, setProgress] = useState(0);
  const hasCalledRef = useRef(false);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const { mutateAsync: processCV } = useProcessCVToApply();
  const [showRetryButton, setShowRetryButton] = useState(false);

  // useEffect(() => {
  //   const duration = 6000;
  //   const target = 90;
  //   const stepTime = 100; // ms
  //   const step = (target / duration) * stepTime;

  //   progressIntervalRef.current = setInterval(() => {
  //     setProgress((prev) => {
  //       if (prev + step >= target) {
  //         if (progressIntervalRef.current)
  //           clearInterval(progressIntervalRef.current);
  //         return target;
  //       }
  //       return prev + step;
  //     });
  //   }, stepTime);

  //   return () => {
  //     if (progressIntervalRef.current) {
  //       clearInterval(progressIntervalRef.current);
  //       progressIntervalRef.current = null;
  //     }
  //   };
  // }, []);

  const startProgress = () => {
    const duration = 6000;
    const target = 90;
    const stepTime = 100; // ms
    const step = (target / duration) * stepTime;
    setProgress(0);

    progressIntervalRef.current = setInterval(() => {
      setProgress((prev) => {
        if (prev + step >= target) {
          if (progressIntervalRef.current)
            clearInterval(progressIntervalRef.current);
          return target;
        }
        return prev + step;
      });
    }, stepTime);
  };

  const startProcessingCV = async () => {
    startProgress();
    setShowRetryButton(false);
    try {
      const res = await processCV({
        jobId,
        simulationId,
        cvFile: cvData.type === 'upload' ? cvData.file : undefined,
        useProfileCV: cvData.type === 'profile',
      });
      setTimeout(() => {
        onSuccess?.(res);
      }, DELAY_PROGRESS);
    } catch (error: any) {
      const userError = getUserError(
        error,
        'Failed to process CV. Please try again later.'
      );
      setTimeout(() => {
        userError.code === 'JOB_APPLIED'
          ? toast.success(userError.message)
          : toast.error(userError.message);
        if (userError.code === 'JOB_APPLIED' && !!onErrorJobApplied) {
          onErrorJobApplied?.();
        } else setShowRetryButton(true);
      }, DELAY_PROGRESS);
    }

    setTimeout(() => {
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
        progressIntervalRef.current = null;
      }
      setProgress(100);
    }, 1500);
  };

  useEffect(() => {
    if (hasCalledRef.current) return;

    hasCalledRef.current = true;
    startProcessingCV();
  }, []);

  return (
    <div className="flex flex-col items-center gap-5 p-6 text-center">
      <img src="/job/cv-edit.png" alt="" className="h-auto w-36" />
      <p className="text-xl font-bold text-[#484848]">Processing your CV...</p>
      <div className="text-[16px]">
        {!showRetryButton ? (
          <>
            <p>
              The system is analyzing the CV and job simulation information.
            </p>
            <p>Please wait a moment ...</p>
          </>
        ) : (
          <>
            <p>The CV processing failed. Please try again.</p>
          </>
        )}
      </div>
      <div className="w-full">
        {!showRetryButton ? (
          <>
            <Progressbar
              value={progress}
              trackClassName="bg-gray-200 [&>div]:transition-all [&>div]:duration-100"
              labelClassName="hidden"
            />
            <div className="flex flex-row">
              <span className="ml-auto">{Math.round(progress)}%</span>
            </div>
          </>
        ) : (
          <Button className="text-white" onClick={startProcessingCV}>
            Try again
          </Button>
        )}
      </div>
    </div>
  );
};

interface TaskSubmission {
  title: string;
  description: string;
  submission: {
    submittedAt: Date;
    content: string;
  };
}

const TaskSlider = ({
  tasks,
  onTasksUpdate,
}: {
  tasks: { title: string; description: string; exampleSubmission: string }[];
  onTasksUpdate: (tasks: TaskSubmission[]) => void;
}) => {
  const [currentTaskIndex, setCurrentTaskIndex] = useState(0);
  const [taskSubmissions, setTaskSubmissions] = useState<TaskSubmission[]>(
    tasks.map((task) => ({
      title: task.title,
      description: task.description,
      submission: {
        submittedAt: new Date(),
        content: '',
      },
    }))
  );

  const currentTask = tasks[currentTaskIndex];
  const currentSubmission = taskSubmissions[currentTaskIndex];

  const updateCurrentSubmission = (content: string) => {
    const updatedSubmissions = [...taskSubmissions];
    updatedSubmissions[currentTaskIndex] = {
      ...updatedSubmissions[currentTaskIndex],
      submission: {
        submittedAt: new Date(),
        content,
      },
    };
    setTaskSubmissions(updatedSubmissions);
    onTasksUpdate(updatedSubmissions);
  };

  const goToNext = () => {
    if (currentTaskIndex < tasks.length - 1) {
      setCurrentTaskIndex(currentTaskIndex + 1);
    }
  };

  const goToPrevious = () => {
    if (currentTaskIndex > 0) {
      setCurrentTaskIndex(currentTaskIndex - 1);
    }
  };

  useEffect(() => {
    onTasksUpdate(taskSubmissions);
  }, []);

  return (
    <div className="space-y-6">
      {/* Tasks Indicators */}
      <div className="flex items-center justify-between">
        <h3 className="font-semibold">
          Tasks ({currentTaskIndex + 1}/{tasks.length})
        </h3>
        <div className="flex space-x-2">
          {tasks.map((_, index) => (
            <div
              key={index}
              className={cn(
                'h-3 w-3 cursor-pointer rounded-full transition-colors',
                index === currentTaskIndex ? 'bg-primary' : 'bg-gray-300',
                taskSubmissions[index]?.submission?.content && 'bg-green-500'
              )}
              onClick={() => setCurrentTaskIndex(index)}
            />
          ))}
        </div>
      </div>

      <div className="space-y-4 rounded-lg border p-6">
        <div>
          <h4 className="mb-2 text-lg font-semibold">{currentTask.title}</h4>
          {/* <p className="mb-4 whitespace-pre-line text-sm text-gray-700">
            {currentTask.description}
          </p> */}
          <SimpleMarkdown
            content={currentTask.description}
            className="text-sm"
          />
        </div>

        {/* Example submission */}
        {/* <div className="rounded-lg bg-gray-50 p-4">
          <p className="mb-2 text-sm font-medium text-gray-600">
            Example Submission:
          </p>
          <p className="whitespace-pre-wrap text-sm text-gray-700">
            {currentTask.exampleSubmission}
          </p>
        </div> */}

        <div>
          <label className="mb-2 block">
            <span className="font-bold">Your Submission</span>
            <span className='ml-1 text-sm text-gray-500'>(optional)</span>:
          </label>
          <textarea
            value={currentSubmission?.submission?.content || ''}
            onChange={(e) => updateCurrentSubmission(e.target.value)}
            placeholder="Enter your submission here..."
            className="h-32 w-full resize-none rounded-lg border border-gray-300 p-3 text-sm focus:border-transparent focus:ring-2 focus:ring-primary"
          />
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={goToPrevious}
          disabled={currentTaskIndex === 0}
          className="flex items-center gap-2"
        >
          <ChevronLeft className="h-4 w-4" />
          Previous
        </Button>
        <Button
          onClick={goToNext}
          disabled={currentTaskIndex === tasks.length - 1}
          className="flex items-center gap-2 bg-primary text-white"
        >
          Next
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

const ApplyCVOverviewStep = ({
  result,
  simulationId,
  onClose,
  onSuccess,
  onErrorJobApplied,
}: {
  result: ProcessCVToApplyResponse;
  simulationId: string;
  onClose: () => void;
  onSuccess: () => void;
  onErrorJobApplied?: () => void;
}) => {
  // const isFailed = result.status === 'failed';

  const aiOverview = [...(result.overview || []), ...(result.feedback || [])];
  const [taskSubmissions, setTaskSubmissions] = useState<TaskSubmission[]>([]);

  const [user] = useAtom(userAtom);

  const { mutateAsync: applyCV, isPending } = useApplyCV();

  const handleTasksUpdate = (tasks: TaskSubmission[]) => {
    setTaskSubmissions(tasks);
  };

  const handleApplyCV = async () => {
    if (!result) return;

    try {
      await applyCV({
        processId: result.id,
        tasks: taskSubmissions.length > 0 ? taskSubmissions : undefined,
      });
      toast.success('Successfully applied for the job.');
      onSuccess?.();
      onClose?.();
    } catch (error: any) {
      const userError = getUserError(
        error,
        'Failed to apply for the job. Please try again later.'
      );
      toast.error(userError.message);

      if (userError.code === 'JOB_APPLIED') {
        onErrorJobApplied?.();
      }
    }
  };

  return (
    <div className="flex flex-col items-center gap-6 p-8 text-center">
      {/* Top icon */}
      <div className="flex items-center justify-center">
        <img
          className="w-[90px]"
          // src={isFailed ? '/circle-x.png' : '/circle-check.png'}
          src={'/circle-check.png'}
        />
      </div>

      {/* Title */}
      <p className="text-2xl font-bold text-[#1B1C1E]">
        {/* {isFailed ? 'Application Not Successful' : 'Congratulations!'} */}
        CV Match Result
      </p>

      {/* Subtitle */}
      <p className="text-[16px] text-[#5E5E5E]">
        We analyzed your CV against the job requirements. Here’s how well your
        profile aligns with this role.
      </p>

      {/* Candidate card (pure Tailwind) */}
      <div className="w-full overflow-hidden rounded-2xl border border-gray-200 bg-white text-left shadow">
        {/* Header row */}
        <div className="flex flex-col gap-2 border-b border-gray-200 p-5 md:flex-row md:items-center md:justify-between">
          <div>
            <p className="text-lg font-bold text-[#1B1C1E]">
              {user?.firstName} {user?.lastName}
            </p>
            <p className="text-sm">
              <span className="text-gray-500">Position:</span> {result.position}
            </p>
            {/* <p className="text-sm">
              <span className="text-gray-500">Status:</span>{' '}
              <span
                className={`font-semibold ${
                  isFailed ? 'text-red-600' : 'text-green-600'
                }`}
              >
                {isFailed ? 'Fail' : 'Pass'}
              </span>
            </p> */}
          </div>
          <div className="flex flex-col items-center justify-center text-right">
            <p className="text-sm text-gray-500">Match</p>
            <p className="text-2xl font-bold text-[#1B1C1E]">
              {result.matchPercentage}%
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6 p-5">
          {/* AI Overview */}
          {aiOverview.length > 0 && (
            <div className="rounded-lg border p-4">
              <div className="mb-3 flex items-center gap-2">
                <Sparkles className="h-4 w-4" />
                <span className="text-[15px] font-medium">AI Overview</span>
              </div>
              <ul className="ml-4 list-disc space-y-1 text-sm text-gray-700">
                {aiOverview.map((item, idx) => (
                  <li key={idx}>{item}</li>
                ))}
              </ul>
            </div>
          )}

          {/* TASKS */}
          {(result.tasks || [])?.length > 0 && (
            <TaskSlider
              tasks={result.tasks}
              onTasksUpdate={handleTasksUpdate}
            />
          )}
        </div>
      </div>

      <p className="text-xs">
        * We encourage you to take the job simulation to demonstrate your true
        capabilities and boost your chances of success.
      </p>

      <div className="flex flex-row gap-3">
        <Button
          variant="outline"
          onClick={() => onClose?.()}
          disabled={isPending}
        >
          Cancel
        </Button>
        <StartSimulationButton
          simId={simulationId}
          buttonProps={{
            variant: 'solid',
            className: 'bg-[#0D1321] text-white',
            disabled: isPending,
          }}
        >
          Try the Job Simulation
        </StartSimulationButton>
        <Button
          variant="solid"
          className="bg-[#0D1321] text-white hover:bg-[#0b0f1b]"
          disabled={!result || isPending}
          onClick={handleApplyCV}
        >
          Continue
        </Button>
      </div>
    </div>
  );
};

export default function ApplyCVModal({
  open,
  jobId,
  simulationId,
  setOpen,
  onApplySuccess,
  onErrorJobApplied,
}: IProps) {
  const [step, setStep] = useState<number>(0);
  const [result, setResult] = useState<ProcessCVToApplyResponse | null>(null);
  const [cvData, setCvData] = useState<CVSelectionData | null>(null);
  // const router = useRouter();

  // const [user] = useAtom(userAtom);

  const handleCVSelection = (data: CVSelectionData) => {
    setCvData(data);
    setStep(1);
  };

  const handleProcessCVSuccess = (result: ProcessCVToApplyResponse | null) => {
    setResult(result);
    setStep(2);
    // Handle success
  };

  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <CVSelectionStep
            onNext={handleCVSelection}
            onClose={() => setOpen(false)}
          />
        );
      case 1:
        return cvData ? (
          <ApplyCVProcessingCVStep
            jobId={jobId}
            simulationId={simulationId}
            cvData={cvData}
            onSuccess={handleProcessCVSuccess}
            onErrorJobApplied={onErrorJobApplied}
          />
        ) : (
          <>No CV Data</>
        );
      case 2:
        return result ? (
          <ApplyCVOverviewStep
            result={result}
            simulationId={simulationId}
            onClose={() => setOpen(false)}
            onSuccess={onApplySuccess}
            onErrorJobApplied={onErrorJobApplied}
          />
        ) : (
          <>No Result</>
        );
      default:
        return null;
    }
  };

  const getModalClassName = () => {
    if (step === 0) {
      return 'md:w-[800px] md:max-w-[800px]';
    }
    if (step === 1) {
      return 'md:w-[600px] md:max-w-[600px]';
    }
    return 'md:w-[1080px] md:max-w-[1080px]';
  };

  return (
    <Modal
      isOpen={open}
      onClose={() => setOpen(false)}
      containerClassName={getModalClassName()}
    >
      <div className="p-2">{renderStep()}</div>
    </Modal>
  );
}
