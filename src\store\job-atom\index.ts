import { atom } from 'jotai';

export interface JobSearchParams {
  title: string;
  location: string;
}

export interface JobFilterParams {
  jobType: string[];
  difficulty: string[];
  salary: string[];
  categories: string;
}

export const titleAtom = atom<string>('');
export const locationAtom = atom<string>('');

export const jobTypeAtom = atom<string[]>([]);
export const difficultyAtom = atom<string[]>([]);
export const salaryAtom = atom<string[]>([]);
export const categoriesAtom = atom<string>('');

export const jobSearchParamsAtom = atom<JobSearchParams>((get) => ({
  title: get(titleAtom),
  location: get(locationAtom),
}));

export const jobFilterParamsAtom = atom<JobFilterParams>((get) => ({
  jobType: get(jobTypeAtom),
  difficulty: get(difficultyAtom),
  salary: get(salaryAtom),
  categories: get(categoriesAtom),
}));

export const resetFiltersAtom = atom(null, (get, set) => {
  set(jobTypeAtom, []);
  set(difficultyAtom, []);
  set(salaryAtom, []);
  set(categoriesAtom, '');
  set(currentPageAtom, 1);
});

export const resetSearchAtom = atom(null, (get, set) => {
  set(titleAtom, '');
  set(locationAtom, '');
  set(currentPageAtom, 1);
});

export const currentPageAtom = atom<number>(1);
