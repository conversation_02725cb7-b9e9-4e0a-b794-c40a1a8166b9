'use client';

import { useCallback } from 'react';
import ProfileDetail from './profile-detail';
import AllApplication from './all-application';
import UserCV from './user-cv';
import { UserProfile } from '@/api-requests/user-profile';

interface ProfileTabsProps {
  activeTab: string;
  tabs: { name: string; hash: string }[];
  refetch?: () => void;
  profile: UserProfile | null;
}
export default function ProfileTab({
  activeTab,
  tabs,
  refetch,
  profile,
}: ProfileTabsProps) {
  const renderTabContent = useCallback(() => {
    switch (activeTab) {
      case 'profile':
        return <ProfileDetail refetch={refetch} profile={profile} />;
      case 'cv':
        return <UserCV />;
      case 'applications':
        return <AllApplication />;
    }
  }, [activeTab, tabs, profile]);

  return <div>{renderTabContent()}</div>;
}
