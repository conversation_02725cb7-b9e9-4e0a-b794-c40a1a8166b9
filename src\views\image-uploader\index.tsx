'use client';

import { useRef, useState, ReactNode } from 'react';

type RenderArgs = {
  open: () => void;
  dragActive: boolean;
  getRootProps: () => {
    onDrop: (e: React.DragEvent) => void;
    onDragOver: (e: React.DragEvent) => void;
    onClick: () => void;
  };
  getInputProps: () => {
    accept: string;
    multiple: boolean;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  };
};

export interface ImageUploaderProps {
  onChange: (file: File | null) => void;
  onError?: (message: string) => void;
  accept?: string;
  maxSize?: number;
  multiple?: boolean;
  children: (args: RenderArgs) => ReactNode;
}

export default function ImageUploader({
  onChange,
  onError,
  accept = 'image/png,image/jpeg,image/jpg,image/gif,image/webp',
  maxSize = 1024 * 1024,
  multiple = false,
  children,
}: ImageUploaderProps) {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [dragActive, setDragActive] = useState(false);

  const validateAndPick = (files: FileList | null) => {
    if (!files || !files[0]) return;
    const f = files[0];

    const acceptedTypes = accept.split(',').map((type) => type.trim());
    const isValidType = acceptedTypes.some((acceptedType) => {
      if (acceptedType.includes('*')) {
        const baseType = acceptedType.split('/')[0];
        return f.type.startsWith(baseType + '/');
      }
      return f.type === acceptedType;
    });

    if (!isValidType) {
      const typeNames = acceptedTypes
        .map((type) => {
          if (type.includes('*')) return type;
          return type.split('/')[1]?.toUpperCase() || type;
        })
        .join(', ');
      onError?.(`Only ${typeNames} files are allowed.`);
      return;
    }
    if (f.size > maxSize) {
      onError?.('Max size is 1MB.');
      return;
    }
    onChange(f);
  };

  const open = () => inputRef.current?.click();

  const onDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    validateAndPick(e.dataTransfer.files);
  };

  const onDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (!dragActive) setDragActive(true);
  };

  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    validateAndPick(e.target.files);
    if (inputRef.current) inputRef.current.value = '';
  };

  const getRootProps = () => ({
    onDrop,
    onDragOver,
    onClick: open,
  });

  const getInputProps = () => ({
    accept,
    multiple,
    onChange: onInputChange,
  });

  return (
    <>
      <input
        ref={inputRef}
        type="file"
        className="hidden"
        {...getInputProps()}
      />
      {children({ open, dragActive, getRootProps, getInputProps })}
    </>
  );
}
