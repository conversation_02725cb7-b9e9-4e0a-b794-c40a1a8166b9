import { UserCV } from '@/store/user-atom';
import { Job } from '../job/types';
import { AdminSimulation } from '../simulation';
import { User } from '../user/types';
import { UserProfile } from '../user-profile';

export enum JobCandidateQueryKeys {
  GET_CANDIDATE_BY_JOB = 'getCandidateByJob',
  GET_CANDIDATE_BY_ORG = 'getCandidateByOrg',
  GET_CANDIDATE_FOR_ADMIN = 'getCandidateForAdmin',
  GET_ORG_CANDIDATES_QUICK_ANALYTICS = 'getOrgCandidatesQuickAnalytics',
}

export const LIMIT = 10;

export interface OrgCandidateListParams {
  page: number;
  limit: number;
  jobId?: string;
  email?: string;
  name?: string;
  status?: string;
  orgId?: string;
  applyMode?: string;
  sort?: string;
}

export interface OrgCandidateAdminListParams {
  page: number;
  limit: number;
  email?: string;
  candidateName?: string;
  orgName?: string;
  status?: string;
}

export interface ListJobCandidateParams {
  limit?: number;
  page?: number;
  title?: string;
  location?: string;
  categories?: string;
  jobType?: string;
  salary?: string;
  level?: string;
}

export enum ApplicationStatus {
  PENDING = 'pending',
  SCREENING = 'screening',
  INTERVIEW = 'interview',
  OFFER = 'offer',
  REJECTED = 'rejected',
  WITHDRAWN = 'withdrawn',
  HIRED = 'hired',
}

export interface JobCandidate {
  _id: string;
  userId: string;
  email: string;
  simulationId: string;
  jobId?: string;
  orgId?: string;
  applicationStatus: ApplicationStatus;
  simulationStatus: 'active' | 'completed';
  // TODO: startedAt is deprecated
  startedAt: Date;
  appliedAt: Date;
  completedAt?: Date;
  scores?: number;
  matchPercentage?: number;
  cvData?: UserCV;
  aiEvaluation?: {
    summary?: string;
    areasForImprovement?: any[];
    strengths?: any[];
    personalities?: { name: string; explanation: string }[];
    tasks?: {
      title: string;
      description: string;
      submissions?: any[];
      exampleSubmission?: string;
    }[];
    skills?: {
      hardSkills?: { name: string; rating: number }[];
      softSkills?: { name: string; rating: number }[];
    };
    cvProcessResult?: {
      cvData: UserCV;
      result: {
        matchPercentage: number;
        overview?: string[];
        feedback?: string[];
        strengths?: string[];
        areasForImprovement?: string[];
        tasks?: {
          title: string;
          description: string;
          exampleSubmission: string;
        }[];
      };
    };
    risks?: {
      name: string;
      level: string;
      explanation: string;
      mitigation?: string;
    }[];
  };
  tasks?: {
    title: string;
    description: string;
    submissions?: {
      submittedAt: Date;
      content: string;
    }[];
    submission?: {
      submittedAt: Date;
      content: string;
    };
  }[];
  // cvFile only exists when applyMode = cv
  cvFile?: string;
  userProfileSnapshot?: UserProfile;
  quickQuestions?: {
    id: string;
    text: string;
    type: 'text' | 'number' | 'yes_no' | 'single' | 'multiple';
    required: boolean;
    // Use for 'single' and 'multiple' type
    options?: any[];
    answer?: any;
  }[];
  applyMode: 'cv' | 'simulation';
  user?: User;
  simulation?: AdminSimulation;
  job?: Job;
}

export interface QuickAnalyticsResponse {
  totalCandidates: number;
  totalApplications: number;
  totalInProgressCandidates: number;
  last7DaysTotalCandidates: number;
  last30DaysTotalCandidates: number;
}
