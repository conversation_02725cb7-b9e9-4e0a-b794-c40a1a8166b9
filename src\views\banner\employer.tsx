'use client';

import { OrganizationType } from '@/api-requests/organization';
import { Role } from '@/api-requests/user/types';
import { orgAtom } from '@/store/organization-atom';
import { userAtom } from '@/store/user-atom';
import cn from '@/utils/class-names';
import { useAtom } from 'jotai';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import CountUp from 'react-countup';
import { Button } from 'rizzui';
import SignInModal from '../auth/sign-in-up/sign-in-modal';

export default function EmployerBanner() {
  const [org] = useAtom(orgAtom);
  const [user] = useAtom(userAtom);
  const [currentLink, setCurrentLink] = useState<string | null>(null);

  const router = useRouter();

  const [openLoginDialog, setOpenLoginDialog] = useState(false);

  const handleGotoOrgAdminRoute = (e: React.MouseEvent<HTMLAnchorElement>) => {
    const link = e.currentTarget.href;
    e.preventDefault();
    if (!user) {
      setCurrentLink(link);
      setOpenLoginDialog(true);
      return;
    }
    if (link) router.push(link);
  };

  return (
    <>
      <section
        className="relative bg-cover bg-center bg-no-repeat px-4 py-16 xl:px-0"
        style={{ backgroundImage: 'url("/job/job-hero-bg.png")' }}
      >
        <div className="mx-auto flex max-w-[1200px] flex-col items-center justify-between gap-8 lg:flex-row">
          {/* Left section */}
          <div className="max-w-lg text-center lg:text-left">
            <h2 className="text-3xl font-bold text-gray-900 md:text-3xl">
              Hire Based On Skills, <br className="hidden sm:block" /> Not Just
              CVs
            </h2>
            <p className="mt-4 text-base text-gray-600">
              Connect with candidates through job simulation that show you
              exactly what they can do. Reduce hiring tasks and find the right
              fit faster.
            </p>
            <div className="mt-6 flex flex-col items-center justify-center gap-4 sm:flex-row lg:justify-start">
              <Link
                href={
                  org?.type &&
                  [
                    OrganizationType.EDUCATION,
                    OrganizationType.COMMUNITY,
                  ].includes(org.type)
                    ? '/org/admin/simulations'
                    : '/org/admin/jobs'
                }
                onClick={(e) => handleGotoOrgAdminRoute(e)}
              >
                <Button className="bg-primary text-white">
                  {org?.type &&
                  [
                    OrganizationType.EDUCATION,
                    OrganizationType.COMMUNITY,
                  ].includes(org.type)
                    ? 'View Simulations'
                    : 'View Jobs'}
                </Button>
              </Link>
              <Link
                href={'/org/admin/candidates'}
                onClick={(e) => handleGotoOrgAdminRoute(e)}
              >
                <Button variant="flat" className="bg-white shadow-sm">
                  View Candidates
                </Button>
              </Link>
            </div>
          </div>

          {/* Right section */}
          <div className="grid w-full max-w-md grid-cols-2 overflow-hidden rounded-2xl bg-primary text-white">
            {[
              { number: 2147, label: 'Active Jobs' },
              { number: 84, label: 'Employer Satisfaction', suffix: '%' },
              { number: 3.2, label: 'Faster Hiring', decimals: 1, suffix: 'x' },
              { number: 450, label: 'Companies', suffix: '+' },
            ].map((item, index) => (
              <div
                key={index}
                className={cn(
                  'flex flex-col items-center justify-center p-6 text-center',
                  index % 2 === 0 ? 'border-r border-white/50' : '',
                  index < 2 ? 'border-b border-white/50' : ''
                )}
              >
                <div className="text-3xl font-bold">
                  <CountUp
                    start={0}
                    end={item.number}
                    duration={2}
                    decimals={item.decimals || 0}
                    suffix={item.suffix || ''}
                  />
                </div>
                <div className="mt-1 text-sm">{item.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>
      {openLoginDialog && (
        <SignInModal
          open={openLoginDialog}
          onClose={() => setOpenLoginDialog(false)}
          role={Role.EMPLOYER}
          onLoginSuccess={() => {
            if (currentLink) {
              setCurrentLink(null);
              router.push(currentLink);
            }
          }}
        />
      )}
    </>
  );
}
