'use client';

/**
 * Example:
 * 
 * import { exportToExcel } from '@/utils/export-to-excel';
 *
 * exportToExcel(
           ['CANDIDATE NAME', 'MATCHES', 'STATUS', 'AI REVIEW'],
           [
             [
               'Lager Ho',
               '86%',
               'active',
               'As a Junior Data Analyst at DataScope Inc, you’ll play a key role ...As a Junior Data Analyst at DataScope Inc, you’ll play a key role ...As a Junior Data Analyst at DataScope Inc, you’ll play a key role ...',
             ],
             [
               'Lager Ho',
               '86%',
               'active',
               'As a Junior Data Analyst at DataScope Inc, you’ll play a key role ...As a Junior Data Analyst at DataScope Inc, you’ll play a key role ...As a Junior Data Analyst at DataScope Inc, you’ll play a key role ...As a Junior Data Analyst at DataScope Inc, you’ll play a key role ...',
             ],
           ],
           {
             fileName: 'candidates',
             noName: 'No',
             maxColWidth: 50,
             maxWidthsByHeader: { 'AI REVIEW': 80 },
             wrap: true,
             autoFit: true,
             autoRowHeight: true,
             alignDefault: 'left', // mặc định căn trái
             alignByHeader: { MATCHES: 'right' },
           }
         );
 */

export type HAlign = 'left' | 'right' | 'auto';

export interface ExportOptions {
  fileName?: string;
  sheetName?: string;
  /** Nếu truyền (vd 'STT' hoặc 'No.'), sẽ thêm cột số thứ tự đầu tiên */
  noName?: string;
  autoFit?: boolean;

  wrap?: boolean;
  maxColWidth?: number;
  maxWidthsByHeader?: Record<string, number>;
  autoRowHeight?: boolean;

  /** Căn lề mặc định cho toàn bộ cột (mặc định: 'left') */
  alignDefault?: HAlign;
  /** Căn lề theo header cụ thể (ưu tiên hơn alignDefault) */
  alignByHeader?: Record<string, HAlign>;
}

/** bọc chữ mềm theo maxChars bằng \n */
function softWrap(str: string, maxChars: number): string {
  const s = String(str ?? '');
  if (!maxChars || s.length <= maxChars) return s;
  const words = s.split(/\s+/);
  const lines: string[] = [];
  let line = '';
  for (const w of words) {
    if (!line) {
      line = w;
      continue;
    }
    if ((line + ' ' + w).length <= maxChars) line += ' ' + w;
    else {
      lines.push(line);
      line = w;
    }
  }
  if (line) lines.push(line);
  return lines.join('\n');
}

function widthFromColumn(
  values: (string | number | null | undefined)[],
  min = 8,
  max = 50
) {
  const longest = Math.max(
    ...values.map((v) =>
      String(v ?? '')
        .split('\n')
        .reduce((m, ln) => Math.max(m, ln.length), 0)
    ),
    0
  );
  return { wch: Math.min(Math.max(longest + 2, min), max) };
}

/** ép kiểu để “gợi ý” alignment cho Excel */
function coerceByAlign(
  value: string | number | null | undefined,
  align: HAlign
) {
  if (value == null) return value;
  if (align === 'left') {
    // ép thành text
    return String(value);
  }
  if (align === 'right') {
    // nếu là số dạng string => convert sang number
    if (typeof value === 'number') return value;
    const s = String(value).trim();
    // không đụng các giá trị có ký tự %, chữ…
    if (/^-?\d+(\.\d+)?$/.test(s)) return Number(s);
    return value; // để nguyên nếu không phải số thuần
  }
  return value; // auto
}

export async function exportToExcel(
  titles: string[],
  rows: (string | number | null | undefined)[][],
  options: ExportOptions = {}
) {
  const XLSX = await import('xlsx');

  const {
    fileName = 'table',
    sheetName = 'Sheet1',
    noName,
    autoFit = true,

    wrap = true,
    maxColWidth = 50,
    maxWidthsByHeader = {},
    autoRowHeight = true,

    alignDefault = 'left',
    alignByHeader = {},
  } = options;

  // Headers & dữ liệu gốc
  const headers = noName ? [noName, ...titles] : [...titles];
  const rawData = noName ? rows.map((r, i) => [i + 1, ...r]) : rows;

  // Wrap + Alignment coercion theo từng cột
  const processed = rawData.map((row) =>
    row.map((val, colIdx) => {
      const header = headers[colIdx];
      const cap = maxWidthsByHeader[header] ?? maxColWidth;
      let v = val;

      // wrap (chỉ áp dụng cho string)
      if (wrap && typeof v === 'string') v = softWrap(v, cap);

      // xác định align cho cột này
      const align: HAlign = alignByHeader[header] ?? alignDefault;

      // ép kiểu để gợi ý căn lề (left → string, right → number)
      v = coerceByAlign(v, align);
      return v;
    })
  );

  // Tạo sheet
  const matrix = [headers, ...processed];
  const ws = XLSX.utils.aoa_to_sheet(matrix);

  // Auto-fit width
  if (autoFit) {
    const colWidths = headers.map((h, colIdx) => {
      const cap = maxWidthsByHeader[h] ?? maxColWidth;
      const columnValues = [h, ...processed.map((r) => r[colIdx])];
      return widthFromColumn(columnValues, 8, cap);
    });
    (ws as any)['!cols'] = colWidths;
  }

  // Ước lượng chiều cao dòng
  if (autoRowHeight) {
    const rowsHeights: { hpt: number }[] = [];
    const baseHpt = 15;
    const headerLines = headers
      .map((h) => String(h).split('\n').length)
      .reduce((m, x) => Math.max(m, x), 1);
    rowsHeights.push({ hpt: baseHpt * headerLines });

    for (const r of processed) {
      const maxLines = r
        .map((v) => String(v ?? '').split('\n').length)
        .reduce((m, x) => Math.max(m, x), 1);
      rowsHeights.push({ hpt: baseHpt * maxLines });
    }
    (ws as any)['!rows'] = rowsHeights;
  }

  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, sheetName);
  XLSX.writeFile(wb, fileName + '.xlsx');
}
