import axiosInstance from '@/utils/http-client';
import { Job, JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function deleteJob(jobId: string) {
  try {
    const response = await axiosInstance.delete(
      API_ENDPONTS.GET_BY_JOBID.replace(':jobId', jobId)
    );
    return response.data;
  } catch (error: any) {
    return {
      isSuccess: false,
      message: error?.response?.data?.message,
    };
  }
}

export const useDeleteJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobId: string) => deleteJob(jobId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.DELETE_JOB],
      });
    },
  });
};
