import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ShortlistCandidateQueryKeys,
  ShortlistCandidate,
  CreateParams,
} from './types';

export async function createShortlistCandidate(
  payload: CreateParams
): Promise<ShortlistCandidate> {
  const reps = await axiosInstance.post<ShortlistCandidate>(
    API_ENDPONTS.CREATE_SHORTLIST_CANDIDATE,
    payload
  );
  return reps.data;
}

export const useCreateShortlistCandidate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: CreateParams) => createShortlistCandidate(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShortlistCandidateQueryKeys.CREATE_SHORTLIST_CANDIDATE],
      });
    },
  });
};
