'use client';

import Image from 'next/image';

const features = [
  {
    title: 'The Hiring Process That Works for You',
    description: 'Show employers you’re ready from day one.',
    image: '/job/before-aplly.png',
    alt: 'Stand out by showing real ability, not just words.',
  },
  {
    title: 'Direct to Hiring Managers',
    description: 'Impress with what you can do, not just your CV.',
    image: '/job/show-skill.png',
    alt: 'Direct to Hiring Managers',
  },
  {
    title: 'Smarter Matches, Better Jobs',
    description: 'Our AI connects you with roles that fit your ability.',
    image: '/job/more-job.png',
    alt: 'More Jobs, Smarter Matches',
  },
];

export default function JobFeatureCard() {
  return (
    <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
      {features.map((feature, index) => (
        <div
          key={index}
          className="rounded-2xl bg-white p-6 text-center shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-200 ease-in-out hover:-translate-y-1 hover:scale-102 hover:shadow-[0_8px_40px_rgba(0,0,0,0.25)]"
        >
          <div className="mb-6 flex h-[100px] items-center justify-center">
            <Image
              src={feature.image}
              alt={feature.alt}
              width={235}
              height={100}
              className="h-full w-auto object-contain"
              loader={({ src }) => src}
            />
          </div>
          <h2 className="text-[22px] font-semibold text-gray-900">
            {feature.title}
          </h2>
          <p className="mt-2 text-gray-600">{feature.description}</p>
        </div>
      ))}
    </div>
  );
}
