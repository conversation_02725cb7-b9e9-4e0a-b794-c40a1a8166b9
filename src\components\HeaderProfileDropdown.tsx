import { Role } from '@/api-requests/user/types';
import { useAuthActions } from '@/hooks/use-auth-actions';
import { userAtom, UserInfo } from '@/store/user-atom';
import { useAtom } from 'jotai';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Avatar, Dropdown } from 'rizzui';

const MenuItemsJobSeeker = () => {
  const [user] = useAtom(userAtom);
  const router = useRouter();
  if (!user) return null;

  // const

  // TODO: check if on profile page, use window.location.hash. If on another page, use router.push
  const handleGotoUserProfile = (e: React.MouseEvent<HTMLAnchorElement>) => {
    try {
      e.preventDefault();
      // e.stopPropagation();
      const url = new URL(e.currentTarget.href); // parse string
      if (
        /^\/profile\/[a-zA-Z0-9]+$/.test(window.location.pathname) &&
        /^\/profile\/[a-zA-Z0-9]+$/.test(url.pathname)
      ) {
        if (url.hash) window.location.hash = url.hash;
        else router.push(url.pathname);
      } else {
        router.push(url.href);
      }
    } catch (error) {}
  };

  return (
    <div className="mb-2 pt-2">
      <Dropdown.Item className="hover:bg-primary hover:text-white">
        <Link
          href={`/profile/${user.id}#profile`}
          className="w-full text-left"
          onClick={handleGotoUserProfile}
        >
          Profile
        </Link>
      </Dropdown.Item>
      <Dropdown.Item className="hover:bg-primary hover:text-white">
        <Link
          href={`/profile/${user.id}#saved-jobs`}
          className="w-full text-left"
          onClick={handleGotoUserProfile}
        >
          Saved Jobs
        </Link>
      </Dropdown.Item>
      <Dropdown.Item
        className="hover:bg-primary hover:text-white"
        onClick={() => router.push(`/profile/${user.id}#applications`)}
      >
        <Link
          href={`/profile/${user.id}#applications`}
          className="w-full text-left"
          onClick={handleGotoUserProfile}
        >
          My Applications
        </Link>
      </Dropdown.Item>
    </div>
  );
};

const MenuItemsEmployerPartner = ({ orgId }: { orgId?: string }) => {
  const [user] = useAtom(userAtom);
  if (!user) return null;
  return (
    <>
      <div className="mb-2 pt-2">
        <Dropdown.Item className="hover:bg-primary hover:text-white">
          <Link href={`/org/admin`} className="w-full text-left">
            Dashboard
          </Link>
        </Dropdown.Item>
        <Dropdown.Item className="hover:bg-primary hover:text-white">
          <Link href={`/org/admin/jobs`} className="w-full text-left">
            My Jobs
          </Link>
        </Dropdown.Item>
        <Dropdown.Item className="hover:bg-primary hover:text-white">
          <Link href={`/org/admin/candidates`} className="w-full text-left">
            Candidates
          </Link>
        </Dropdown.Item>
        {/* <Dropdown.Item className="hover:bg-primary hover:text-white">
          <Link
            href={`/org/admin/account-setting`}
            className="w-full text-left"
          >
            Profile
          </Link>
        </Dropdown.Item>
        <Dropdown.Item className="hover:bg-primary hover:text-white">
          <Link
            href={`/org/admin/company-setting`}
            className="w-full text-left"
          >
            Organization Profile
          </Link>
        </Dropdown.Item> */}
      </div>
      {!!orgId && (
        <div className="mb-2 pt-2">
          <Dropdown.Item className="hover:bg-primary hover:text-white">
            <Link href={`/org/${orgId}`} className="w-full text-left">
              Organization Page
            </Link>
          </Dropdown.Item>
        </div>
      )}
    </>
  );
};

export default function HeaderProfileDropdown({
  userInfo,
  orgId,
}: {
  userInfo: UserInfo;
  orgId?: string;
}) {
  const { logout } = useAuthActions();

  if (!userInfo) return null;

  return (
    <Dropdown>
      <Dropdown.Trigger>
        <Avatar
          name={`${userInfo.firstName} ${userInfo.lastName}`}
          src={userInfo.avatar || '/avatar/user-default.png'}
          size="md"
          className="h-10 w-10 cursor-pointer rounded-full !bg-transparent object-cover"
        />
      </Dropdown.Trigger>
      <Dropdown.Menu className="w-fit divide-y">
        <Dropdown.Item className="flex cursor-auto flex-row gap-3">
          <div className="min-h-10 min-w-10">
            <Avatar
              name={`${userInfo.firstName} ${userInfo.lastName}`}
              src={userInfo.avatar || '/avatar/user-default.png'}
              size="md"
              className="h-10 w-10 rounded-full !bg-transparent object-cover"
            />
          </div>
          <div className="text-left">
            <p className="max-w-44 truncate">
              {userInfo.firstName} {userInfo.lastName}
            </p>
            <p className="max-w-44 truncate text-xs">{userInfo.email}</p>
          </div>
        </Dropdown.Item>
        {userInfo.role === Role.USER ? (
          <MenuItemsJobSeeker />
        ) : (
          <MenuItemsEmployerPartner orgId={orgId} />
        )}
        <div className="mb-2 pt-2">
          <Dropdown.Item className="hover:bg-primary hover:text-white">
            <Link href={`/`} className="w-full text-left">
              Help Center
            </Link>
          </Dropdown.Item>
          <Dropdown.Item
            className="hover:bg-primary hover:text-white"
            onClick={() => logout()}
          >
            Logout
          </Dropdown.Item>
        </div>
      </Dropdown.Menu>
    </Dropdown>
  );
}
