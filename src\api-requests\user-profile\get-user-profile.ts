import axiosInstance from '@/utils/http-client';
import { UserProfile, UserProfileQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useQuery } from '@tanstack/react-query';

export async function getUserProfile(userId: string): Promise<UserProfile> {
  const response = await axiosInstance.get<UserProfile>(
    API_ENDPONTS.GET_USER_PROFILE.replace(':userId', userId)
  );
  return response.data;
}

export function useGetUserProfile(userId: string) {
  return useQuery<UserProfile>({
    queryKey: [UserProfileQueryKeys.GET_USER_PROFILE, userId],
    queryFn: () => getUserProfile(userId),
    enabled: !!userId,
  });
}
