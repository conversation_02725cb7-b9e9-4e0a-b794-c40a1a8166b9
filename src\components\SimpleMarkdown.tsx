'use client';

import cn from '@/utils/class-names';
import { ParseOptions } from 'markdown-parser-react/dist/parser';
import dynamic from 'next/dynamic';

const Markdown = dynamic(
  () => import('markdown-parser-react').then((markdown) => markdown),

  {
    ssr: false,
  }
);

interface SimpleMarkdownProps {
  content: string;
  className?: string;
  options?: ParseOptions;
}

export const SimpleMarkdown = ({
  content,
  className,
  options,
}: SimpleMarkdownProps) => {
  return (
    <div className={cn('prose max-w-none', className)}>
      <Markdown content={content} options={options} />
    </div>
  );
};

export default SimpleMarkdown;
