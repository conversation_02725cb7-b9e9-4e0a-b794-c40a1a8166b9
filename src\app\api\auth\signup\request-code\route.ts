import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import FormData from 'form-data';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import { Readable } from 'stream';
import { API_DOMAINS, API_ENDPONTS } from '@/config/endpoint';

function bufferToStream(buffer: Buffer): Readable {
  return Readable.from(buffer);
}

export async function POST(req: NextRequest) {
  try {
    const contentType = req.headers.get('content-type') || '';

    if (contentType.includes('multipart/form-data')) {
      const webFormData = await req.formData();
      const form = new FormData();

      for (const [key, value] of webFormData.entries()) {
        if (value instanceof File) {
          const arrayBuffer = await value.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          const stream = bufferToStream(buffer);

          form.append(key, stream, {
            filename: value.name,
            contentType: value.type,
          });
        } else {
          form.append(key, String(value));
        }
      }

      const { data } = await axios.post(
        API_ENDPONTS.SIGN_UP_REQUEST_CODE,
        form,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          baseURL: API_DOMAINS.BASE_API,
        }
      );

      return NextResponse.json({ status: data?.sent });
    }

    const payload = await req.json();
    const { data } = await axios.post(
      API_ENDPONTS.SIGN_UP_REQUEST_CODE,
      payload,
      {
        baseURL: API_DOMAINS.BASE_API,
      }
    );
    return NextResponse.json({ status: data?.sent });
  } catch (e: any) {
    return NextResponse.json(
      {
        message: e?.response?.data?.message || 'Something went wrong',
      },
      {
        status: e?.response?.status || 400,
      }
    );
  }
}
