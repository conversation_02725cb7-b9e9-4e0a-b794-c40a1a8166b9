import { ActionIcon, Modal, Title } from 'rizzui';
import CloseIcon from '../icons/close';
import Image from 'next/image';
import { User } from '@/api-requests/user';
import { getMonthName } from '../profile/user/user-cv';

interface IProps {
  open: boolean;
  onClose: () => void;
  talent: User;
}

export default function CVDetailModal({ open, onClose, talent }: IProps) {
  const experiences = talent?.profile?.cv?.experiences || [];
  const skills = talent?.profile?.cv?.skills || [];
  const educations = talent?.profile?.cv?.educations || [];

  return (
    <Modal isOpen={open} onClose={onClose} size="lg" customSize={'640px'}>
      <div className="w-full rounded-[20px] p-6 sm:w-[640px]">
        <div className="mb-5 flex items-center justify-between">
          <Title as="h4">CV detail</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Image
                src={talent.avatar || '/avatar/user-default.png'}
                alt={talent.firstName || ''}
                width={100}
                height={100}
                onError={(e: React.SyntheticEvent<HTMLImageElement>) => {
                  e.currentTarget.src = '/avatar/user-default.png';
                }}
                className="h-[100px] w-[100px] rounded-full object-cover"
              />

              <div className="flex w-full items-center justify-between">
                <div className="text-bold line-clamp-1 flex-1">
                  {talent.firstName} {talent.lastName}
                </div>
              </div>
            </div>

            <hr className="text-[#F4F4F4]" />

            <div className="w-full space-y-4">
              <div className="flex flex-row items-center justify-between text-lg font-semibold text-[#0D1321]">
                <span>About</span>
                {/* <ActionIcon
                    variant="outline"
                    size="sm"
                    onClick={() => setAboutModal(true)}
                  >
                    <EditIcon className="h-5 w-5" />
                  </ActionIcon> */}
              </div>
              {talent?.profile?.cv?.about ? (
                <p className="mt-4 whitespace-pre-line">
                  {talent?.profile?.cv?.about}
                </p>
              ) : (
                <p className="text-sm italic">
                  Add an About section to highlight your strengths and career
                  objectives.
                </p>
              )}
            </div>
          </div>

          <hr className="text-[#F4F4F4]" />

          <div>
            <div className="text-lg font-bold">Work experience</div>
            <ul role="list" className="divide-y divide-gray-200">
              {experiences.map((exp) => (
                <div
                  className="grid w-full grid-cols-1 gap-4 py-2 sm:grid-cols-[180px_minmax(0,1fr)]"
                  key={exp.id}
                >
                  <div className="text-sm text-[#0D1321]">
                    {exp.startMonth && getMonthName(Number(exp.startMonth))}{' '}
                    {exp.startYear}{' '}
                    {exp.endYear
                      ? `- ${exp.endMonth && getMonthName(Number(exp.endMonth))}
                        ${exp.endYear}`
                      : '- Present'}
                  </div>
                  <div className="w-full">
                    <div className="flex w-full gap-2">
                      <div>
                        <div className="font-semibold">{exp.title}</div>
                        <p>{exp.company}</p>
                      </div>
                    </div>

                    <p className="my-2 whitespace-pre-line text-sm text-[#0D1321]">
                      {exp.description}
                    </p>
                  </div>
                </div>
              ))}
            </ul>
          </div>

          <hr className="text-[#F4F4F4]" />

          <div>
            <div className="text-lg font-bold">Skill</div>
            <ul role="list" className="divide-y divide-gray-200">
              {skills.map((skill) => (
                <div className="py-2">{skill.skill}</div>
              ))}
            </ul>
          </div>

          <hr className="text-[#F4F4F4]" />

          <div>
            <div className="text-lg font-bold">Education</div>
            <ul role="list" className="divide-y divide-gray-200">
              {educations.map((education) => (
                <div
                  className="grid w-full grid-cols-1 gap-4 py-2 sm:grid-cols-[180px_minmax(0,1fr)]"
                  key={education.id}
                >
                  <div className="text-sm text-[#0D1321]">
                    {education.startMonth &&
                      getMonthName(Number(education.startMonth))}{' '}
                    {education.startYear}{' '}
                    {education.endYear
                      ? `- ${education.endMonth ? getMonthName(Number(education.endMonth)) : ''}
                        ${education.endYear}`
                      : ''}
                  </div>
                  <div className="w-full">
                    <div className="flex w-full flex-row justify-between gap-2">
                      <div>
                        <p className="font-semibold">{education.school}</p>
                        <p className="text-sm text-[#484848]">
                          {[education.degree, education.field, education.grade]
                            .filter(Boolean)
                            .join(', ')}
                        </p>
                      </div>
                    </div>

                    <p className="my-2 whitespace-pre-line text-sm text-[#0D1321]">
                      {education.description}
                    </p>
                  </div>
                </div>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </Modal>
  );
}
