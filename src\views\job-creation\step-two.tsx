'use client';

import {
  Input,
  Radio,
  RadioGroup,
  Checkbox,
  MultiSelectOption,
  MultiSelect,
  Text,
  Select,
  Switch,
} from 'rizzui';
import RangeSlider from '../range-slider';
import FieldLabel from './field-label';
import cn from '@/utils/class-names';
import { FormValues } from './index';
import { Controller, useFormContext } from 'react-hook-form';
import currenciesData from '@/data/currencies.json';
import { useMemo } from 'react';

const periodOptions = [
  { label: 'Hourly', value: 'hourly' },
  { label: 'Daily', value: 'daily' },
  { label: 'Weekly', value: 'weekly' },
  { label: 'Monthly', value: 'monthly' },
  { label: 'Yearly', value: 'yearly' },
];

const salaryLable: Record<string, string> = {
  range: 'Min',
  from: 'From',
  upto: 'Up to',
  exact: 'Amout',
};

export default function StepTwo() {
  const {
    control,
    watch,
    setValue,
    formState: { errors },
  } = useFormContext<FormValues>();

  const salary = watch('salary');

  const currencyOptions = useMemo(
    () =>
      currenciesData.map((curency) => ({
        label: curency.code,
        value: curency.code,
      })),
    []
  );

  return (
    <>
      <div className="space-y-4">
        <div>
          <div className="text-lg">Compensation</div>
          <div className="text-sm text-gray-500">
            Define pay, currency and target headcount
          </div>
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <FieldLabel title="Pay type" />
            <Controller
              name="salary.period"
              control={control}
              rules={{
                required: 'Please select pay type',
              }}
              render={({ field }) => (
                <Select
                  clearable
                  {...field}
                  onClear={() => field.onChange(null)}
                  options={periodOptions}
                  placeholder="Select pay type"
                  className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                />
              )}
            />
            {errors.salary?.period && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {errors.salary.period.message}
              </Text>
            )}
          </div>
          <div>
            <FieldLabel title="Currency" />
            <Controller
              name="salary.currency"
              control={control}
              rules={{
                required: 'Please select currency',
              }}
              render={({ field }) => (
                <Select
                  clearable
                  {...field}
                  onClear={() => field.onChange(null)}
                  options={currencyOptions}
                  placeholder="Select currency"
                  className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
                  searchable
                />
              )}
            />
            {errors.salary?.currency && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {errors.salary.currency.message}
              </Text>
            )}
          </div>
        </div>

        <div>
          <FieldLabel title="Salary display" />
          <Controller
            name="salary.type"
            control={control}
            render={({ field }) => (
              <RadioGroup
                value={field.value as string}
                setValue={(val) => {
                  field.onChange(val);
                }}
                className="flex gap-3"
              >
                <Radio
                  label="Range"
                  value="range"
                  size="sm"
                  className="[&_.rizzui-radio-label]:!text-base"
                />
                <Radio
                  label="From"
                  value="from"
                  size="sm"
                  className="[&_.rizzui-radio-label]:!text-base"
                />
                <Radio
                  label="Up to"
                  value="upto"
                  size="sm"
                  className="[&_.rizzui-radio-label]:!text-base"
                />
                <Radio
                  label="Exact"
                  value="exact"
                  size="sm"
                  className="[&_.rizzui-radio-label]:!text-base"
                />
              </RadioGroup>
            )}
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <FieldLabel title={salaryLable[salary.type]} />
            <Controller
              name="salary.min"
              control={control}
              rules={{
                required: 'Please enter minimum salary',
              }}
              render={({ field }) => (
                <Input
                  {...field}
                  value={field.value || 0}
                  onChange={(e) => {
                    const val = e.target.value || 0;
                    if (val === '' || /^\d+$/.test(val as string)) {
                      field.onChange(val === '' ? null : Number(val));
                    }
                  }}
                  variant="flat"
                  placeholder="Enter salary"
                  className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                  inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                />
              )}
            />
          </div>
          {salary.type === 'range' && (
            <div>
              <FieldLabel title="Max" />
              <Controller
                name="salary.max"
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    value={field.value || 0}
                    onChange={(e) => {
                      const val = e.target.value || 0;
                      if (val === '' || /^\d+$/.test(val as string)) {
                        field.onChange(val === '' ? null : Number(val));
                      }
                    }}
                    variant="flat"
                    placeholder="Enter a Job title (e.g. Software Developer)"
                    className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
            </div>
          )}
        </div>
        {salary.type === 'range' ? (
          <div>
            <Controller
              name="salary"
              control={control}
              render={({ field }) => (
                <RangeSlider
                  range
                  step={100}
                  min={0}
                  max={100000}
                  onChange={(val) => {
                    const [min, max] = val as [number, number];
                    field.onChange({
                      ...field.value,
                      min,
                      max,
                    });
                  }}
                  value={[field.value?.min || 0, field.value?.max || 100000]}
                />
              )}
            />
            <div className="flex justify-between">
              <span>{(salary?.min as number)?.toLocaleString()}</span>
              <span>{(salary?.max as number)?.toLocaleString()}</span>
            </div>
          </div>
        ) : ['from', 'upto'].includes(salary.type) ? (
          <div>
            <Controller
              name="salary.min"
              control={control}
              render={({ field }) => (
                <RangeSlider
                  min={0}
                  max={100000}
                  onChange={(val) => field.onChange(val)}
                  value={field.value}
                />
              )}
            />
            <div className="flex justify-between">
              <span>{(salary?.min as number)?.toLocaleString()}</span>
            </div>
          </div>
        ) : (
          ''
        )}
      </div>
    </>
  );
}
