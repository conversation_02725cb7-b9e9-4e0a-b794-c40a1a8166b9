import { API_ENDPONTS } from '@/config/endpoint';
import { requestPost } from '@/utils/http-client';
import { cleanQueryParams } from '@/utils/url';
import { useMutation } from '@tanstack/react-query';
import { AdminSimulation } from './types';

async function createOrgSimulation(params: {
  orgId: string;
  name: string;
  description: string;
  tasks: { title: string; content: string }[];
}): Promise<AdminSimulation> {
  const response = await requestPost<AdminSimulation>(
    API_ENDPONTS.CREATE_ORG_SIMULATIONS,
    cleanQueryParams(params),
    { orgId: params.orgId }
  );
  return response.data;
}

export function useCreateOrgSimulation() {
  return useMutation({
    mutationFn: (params: {
      orgId: string;
      name: string;
      description: string;
      tasks: { title: string; content: string }[];
    }) => createOrgSimulation(params),
    onSuccess: () => {},
  });
}
