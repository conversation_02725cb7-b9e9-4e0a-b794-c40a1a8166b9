import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Organization, OrganizationQueryKeys } from './types';
import { cleanQueryParams } from '@/utils/url';

export async function updateCompany(
  id: string,
  payload: Organization
): Promise<Organization> {
  const reps = await axiosInstance.put<Organization>(
    API_ENDPONTS.UPDATE_COMPANY.replace(':id', id),
    payload
  );
  return reps.data;
}

export const useUpdateCompany = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: Organization) => updateCompany(id, payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [OrganizationQueryKeys.UPDATE_COMPANY],
      });
    },
  });
};
