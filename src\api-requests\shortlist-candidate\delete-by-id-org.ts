import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ShortlistCandidateQueryKeys,
  ShortlistCandidate,
  CreateParams,
} from './types';

export async function deleteShortListCandidateByIdAndOrg(payload: {
  id: string;
  orgId: string;
}): Promise<ShortlistCandidate> {
  const reps = await axiosInstance.delete<ShortlistCandidate>(
    API_ENDPONTS.DELETE_SHORTLIST_CANDIDATE_BY_ORG.replace(
      ':id',
      payload.id
    ).replace(':orgId', payload.orgId)
  );
  return reps.data;
}

export const useDeleteShortListCandidateByIdAndOrg = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { id: string; orgId: string }) =>
      deleteShortListCandidateByIdAndOrg(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [
          ShortlistCandidateQueryKeys.DELETE_SHORTLIST_CANDIDATE_BY_ID_AND_ORG,
        ],
      });
    },
  });
};
