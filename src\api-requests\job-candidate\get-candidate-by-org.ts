import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { cleanQueryParams } from '@/utils/url';
import { useQuery } from '@tanstack/react-query';
import {
  JobCandidate,
  JobCandidateQueryKeys
} from './types';

export async function getCandidatesByOrg(
  orgId: string,
  candidateId: string
): Promise<JobCandidate> {
  const reps = await requestGet<JobCandidate>(
    API_ENDPONTS.GET_CANDIDATE_BY_ORG,
    {},
    cleanQueryParams({ orgId, candidateId })
  );
  // const reps = await axiosInstance.get<JobCandidate>(
  //   API_ENDPONTS.GET_CANDIDATES_BY_ORG.replace(':orgId', orgId || ''),
  //   {
  //     params: cleanQueryParams({ orgId, candidateId }),
  //   }
  // );
  return reps.data;
}

export function useGetCandidateByOrg(orgId: string, candidateId: string) {
  return useQuery<JobCandidate>({
    queryKey: [
      JobCandidateQueryKeys.GET_CANDIDATE_BY_ORG,
      { orgId, candidateId },
    ],
    queryFn: () => getCandidatesByOrg(orgId, candidateId),
    enabled: !!orgId && !!candidateId,
  });
}
