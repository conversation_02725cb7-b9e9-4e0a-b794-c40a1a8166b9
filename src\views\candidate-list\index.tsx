'use client';

import { Suspense, useState } from 'react';
import CandidatesManagement from './candidates-management';
import ShortlistCandidate from '../shortlist-candidate';

export default function CandidateList() {
  const [view, setView] = useState<'shortlist' | 'candidate'>('candidate');
  return (
    <Suspense fallback={<></>}>
      {view === 'shortlist' ? (
        <ShortlistCandidate setView={setView} />
      ) : (
        <CandidatesManagement setView={setView} />
      )}
    </Suspense>
  );
}
