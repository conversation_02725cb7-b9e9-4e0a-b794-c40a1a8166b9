import { SelectOption } from '@/api-requests/types';

export const applyModeOptions: SelectOption[] = [
  { label: 'Simulation', value: 'simulation' },
  { label: 'CV', value: 'cv' },
];

export const employementOptions: SelectOption[] = [
  { label: 'Full-time', value: 'full_time' },
  { label: 'Part-time', value: 'part_time' },
  { label: 'Contract', value: 'contract' },
];

export const workplaceOptions: SelectOption[] = [
  { label: 'Onsite', value: 'onsite' },
  { label: 'Remote', value: 'remote' },
  { label: 'Hybrid', value: 'hybrid' },
];

export const categoryOptions: SelectOption[] = [
  { label: 'Software Development', value: 'Software Development' },
  { label: 'Web Development', value: 'Web Development' },
  { label: 'Mobile App Development', value: 'Mobile App Development' },
  { label: 'Game Development', value: 'Game Development' },
  { label: 'DevOps & Cloud Engineering', value: 'DevOps & Cloud Engineering' },
  {
    label: 'Data Science & Machine Learning',
    value: 'Data Science & Machine Learning',
  },
  { label: 'Artificial Intelligence', value: 'Artificial Intelligence' },
  { label: 'Cybersecurity', value: 'Cybersecurity' },
  { label: 'Blockchain & Web3', value: 'Blockchain & Web3' },
  { label: 'UI/UX Design', value: 'UI/UX Design' },
  { label: 'Product Management', value: 'Product Management' },
  { label: 'IT Project Management', value: 'IT Project Management' },
  {
    label: 'Quality Assurance & Testing',
    value: 'Quality Assurance & Testing',
  },
  { label: 'Database Administration', value: 'Database Administration' },
  { label: 'Network Engineering', value: 'Network Engineering' },
  { label: 'Embedded Systems & IoT', value: 'Embedded Systems & IoT' },
  { label: 'AR/VR Development', value: 'AR/VR Development' },
  { label: 'IT Support & Helpdesk', value: 'IT Support & Helpdesk' },
  { label: 'Systems Administration', value: 'Systems Administration' },
  {
    label: 'Tech Writing & Documentation',
    value: 'Tech Writing & Documentation',
  },
];

export const levelOptions: SelectOption[] = [
  { label: 'Internship', value: 'Internship' },
  { label: 'Fresher', value: 'Fresher' },
  { label: 'Junior', value: 'Junior' },
  { label: 'Middle', value: 'Middle' },
  { label: 'Senior', value: 'Senior' },
  { label: 'Lead', value: 'Lead' },
  { label: 'Principal Engineer', value: 'Principal Engineer' },
  { label: 'Engineering Manager', value: 'Engineering Manager' },
  { label: 'Director of Engineering', value: 'Director of Engineering' },
  { label: 'CTO', value: 'CTO' },
];
