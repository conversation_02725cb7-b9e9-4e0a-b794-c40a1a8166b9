version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - AWS_ACCOUNT_ID=$(echo $CODEBUILD_BUILD_ARN | cut -f5 -d ':')
      - AWS_REGION=$(echo $CODEBUILD_BUILD_ARN | cut -f4 -d ':')
      - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
      - REPOSITORY_URI=$AWS_ACCOUNT_ID.dkr.ecr.ap-southeast-2.amazonaws.com/mnet
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${COMMIT_HASH:=latest}
      - IMAGE_NAME=ig-career-$ENV-x86
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - ls -al
      - docker build --build-arg ENV=$DOT_ENV -t $REPOSITORY_URI:$IMAGE_NAME-latest .
      - docker tag $REPOSITORY_URI:$IMAGE_NAME-latest $REPOSITORY_URI:$IMAGE_NAME-$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:$IMAGE_NAME-latest
      - docker push $REPOSITORY_URI:$IMAGE_NAME-$IMAGE_TAG
      - echo Writing image definitions file...
      - printf '[{"name":"ig-career","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_NAME-$IMAGE_TAG > imagedefinitions.json
artifacts:
  files: imagedefinitions.json
