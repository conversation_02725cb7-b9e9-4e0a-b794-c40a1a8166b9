import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/**/*.{tsx,css}',
    './node_modules/rizzui/dist/*.{js,ts,jsx,tsx}',
  ],
  theme: {
    extend: {
      colors: {
        // primary: 'var(--primary)',
        // primary: 'rgb(var(--primary-rgb) / <alpha-value>)',
        primary: 'rgb(var(--primary-rgb) / <alpha-value>)',
      },
    },
  },
  plugins: [require('@tailwindcss/typography')],
};

export default config;
