import axiosInstance from '@/utils/http-client';
import { JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function closeJob(payload: { jobId: string; orgId: string }) {
  const response = await axiosInstance.post(
    API_ENDPONTS.CLOSE_JOB.replace(':jobId', payload.jobId),
    { orgId: payload.orgId }
  );
  return response.data;
}

export const useCloseJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { jobId: string; orgId: string }) =>
      closeJob(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.CLOSE_JOB],
      });
    },
  });
};
