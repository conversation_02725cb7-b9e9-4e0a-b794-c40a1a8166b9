import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  ShortlistCandidateQueryKeys,
  ShortlistCandidate,
  BulkCreateParams,
} from './types';

export async function bulkDeleteShortlistCandidate(
  payload: BulkCreateParams
): Promise<ShortlistCandidate> {
  const reps = await axiosInstance.post<ShortlistCandidate>(
    API_ENDPONTS.BULK_DELETE_SHORTLIST_CANDIDATE,
    payload
  );
  return reps.data;
}

export const useBulkDeleteShortlistCandidate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: BulkCreateParams) => bulkDeleteShortlistCandidate(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShortlistCandidateQueryKeys.BULK_DELETE_SHORTLIST_CANDIDATE],
      });
    },
  });
};
