'use client';

import { useMemo, useState } from 'react';
import { Modal, Input, Select, ActionIcon, Title, Button, Textarea } from 'rizzui';
import countriesData from '@/data/countries.json';
import FieldLabel from '@/views/job-creation/field-label';
import CloseIcon from '@/views/icons/close';

interface IProps {
  open: boolean;
  onClose: () => void;
}

interface Option {
  label: string;
  value: string;
}

const genderOptions: Option[] = [
  { label: 'Male', value: 'male' },
  { label: 'Female', value: 'female' },
];

export default function ProfileModal({ open, onClose }: IProps) {
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [region, setRegion] = useState('');
  const [gender, setGender] = useState<Option | null>(null);
  const [country, setCountry] = useState<Option | null>(null);

  const countryOptions = useMemo(
    () =>
      countriesData.map((country) => ({
        label: country.name,
        value: country.code,
      })),
    []
  );

  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div className="w-full rounded-[20px] p-6 sm:w-[450px]">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h3">Edit Profile</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-4">
          <div>
            <FieldLabel title="First name" />
            <Input
              value={firstName}
              onChange={(e) => setFirstName(e.target.value)}
              variant="flat"
              placeholder="Enter your full name"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
              inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
            />
          </div>

          <div>
            <FieldLabel title="Last name" />
            <Input
              value={lastName}
              onChange={(e) => setLastName(e.target.value)}
              variant="flat"
              placeholder="Enter your last name"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
              inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
            />
          </div>

          <div className="w-full">
            <FieldLabel title="Description" />
            <Textarea
              variant="flat"
              placeholder="Enter your description"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
            />
          </div>

          <div>
            <FieldLabel title="Gender" />
            <Select
              clearable
              value={gender}
              onChange={(v: Option) => setGender(v)}
              onClear={() => setGender(null)}
              options={genderOptions}
              placeholder="Select gender"
              className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
            />
          </div>

          <div>
            <FieldLabel title="Phone number" />
            <Input
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              variant="flat"
              placeholder="Enter your phone number"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
              inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
            />
          </div>

          <div>
            <FieldLabel title="Address" />
            <Input
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              variant="flat"
              placeholder="Enter your address"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
              inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
            />
          </div>

          <div>
            <FieldLabel title="City" />
            <Input
              value={city}
              onChange={(e) => setCity(e.target.value)}
              variant="flat"
              placeholder="Enter your city"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
              inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
            />
          </div>

          <div>
            <FieldLabel title="Region" />
            <Input
              value={region}
              onChange={(e) => setRegion(e.target.value)}
              variant="flat"
              placeholder="Enter your region"
              className="w-full [&_*]:rounded-lg [&_*]:!border-0 [&_*]:!border-none [&_*]:bg-[#F4F4F4] [&_*]:!outline-none [&_*]:!ring-0"
              inputClassName="!border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
            />
          </div>

          <div>
            <FieldLabel title="Country" />
            <Select
              clearable
              value={country}
              onChange={(v: Option) => setCountry(v)}
              onClear={() => setCountry(null)}
              options={countryOptions}
              placeholder="Select your country"
              className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-[#F4F4F4] [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
              searchable={true}
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-3">
          <Button variant="outline" className="border-primary">
            Cancel
          </Button>
          <Button className="bg-primary text-white" onClick={onClose}>
            Save
          </Button>
        </div>
      </div>
    </Modal>
  );
}
