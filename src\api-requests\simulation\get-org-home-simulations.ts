import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { HomeSimulation, SimulationQueryKeys } from './types';

import { useInfiniteQuery, useQuery } from '@tanstack/react-query';

interface GetOrgHomeSimulationsResponse {
  data: HomeSimulation[];
  meta: { lastId: string; hasMore: boolean };
}

export async function getOrgHomeSimulations(params: {
  orgId: string;
  limit: number;
  keyword?: string;
  lastId?: string;
  isFeatured?: boolean;
}): Promise<GetOrgHomeSimulationsResponse> {
  const { orgId, limit, keyword, lastId, isFeatured = false } = params;

  const response = await requestGet<GetOrgHomeSimulationsResponse>(
    API_ENDPONTS.GET_ORG_HOME_SIMULATIONS,
    {
      limit,
      ...(keyword && { keyword }),
      ...(lastId && { lastId }),
      isFeatured,
    },
    {
      orgId,
    }
  );

  // const response = await axiosInstance.get<GetOrgHomeSimulationsResponse>(
  //   API_ENDPONTS.GET_ORG_HOME_SIMULATIONS.replace(':orgId', orgId),
  //   {
  //     params: {
  //       limit,
  //       ...(keyword && { keyword }),
  //       ...(lastId && { lastId }),
  //     },
  //   }
  // );
  return response.data;
}

export function useGetOrgFeaturedSimulations(params: { orgId: string }) {
  const query = useQuery<GetOrgHomeSimulationsResponse>({
    queryKey: [SimulationQueryKeys.GET_ORG_FEATURED_SIMULATIONS, params.orgId],
    queryFn: () =>
      getOrgHomeSimulations({
        limit: 6,
        orgId: params.orgId,
        isFeatured: true,
      }),
    enabled: !!params.orgId,
  });
  const simulations = query.data?.data || [];

  return {
    ...query,
    simulations,
  };
}

export function useGetOrgHomeSimulationsInfinite(params: {
  orgId: string;
  keyword?: string;
}) {
  const query = useInfiniteQuery<GetOrgHomeSimulationsResponse, Error>({
    queryKey: [
      SimulationQueryKeys.GET_ORG_HOME_SIMULATIONS,
      params.orgId,
      params.keyword,
    ],
    queryFn: async ({ pageParam }) => {
      const result = await getOrgHomeSimulations({
        ...params,
        limit: 6,
        lastId: (pageParam as string | undefined) || '',
      });
      return result;
    },
    initialPageParam: undefined,
    enabled: !!params.orgId,
    getNextPageParam: (lastPage) =>
      lastPage.meta.hasMore && lastPage.meta.lastId
        ? lastPage.meta.lastId
        : undefined,
    refetchOnWindowFocus: false,
  });

  const simulations = query.data?.pages.flatMap((page) => page.data) ?? [];

  return {
    ...query,
    simulations,
  };
}
