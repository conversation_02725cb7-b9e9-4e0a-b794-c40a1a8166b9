'use client';

import { Organization } from '@/api-requests/organization';
import { updateMedia } from '@/api-requests/organization/update-media';
import { useUpdateCompany } from '@/api-requests/organization/update-org';
import OrgAdminProfileLayout from '@/layouts/org-admin-profile-layout';
import { orgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import CompanyForm from './profile-form';
import CompanyInfo from './company-info';

export type FormValues = {
  name: string;
  address: string;
  city: string;
  region: string;
  country?: {
    label: string;
    value: string;
  } | null;
  website: string;
  email: string;
  description: string;
};

export default function EmployerProfile() {
  const [org, setOrg] = useAtom(orgAtom);

  const [isEdit, setEdit] = useState(false);

  const { mutateAsync } = useUpdateCompany(org?._id as string);

  const methods = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      name: org?.name || '',
      address: org?.address || '',
      city: org?.city || '',
      region: org?.region || '',
      country: org?.country
        ? { label: org.country, value: org.country.toLowerCase() }
        : null,
      // website: org?.website ? org.website.replace(/^https?:\/\//i, '') : '',
      website: org?.website ? org.website.replace(/^https?:\/\//i, '') : '',
      email: org?.email || '',
      description: org?.description || '',
    },
  });

  const {
    handleSubmit,
    reset,
    formState: { isValid, isSubmitting },
  } = methods;

  useEffect(() => {
    reset({
      name: org?.name || '',
      address: org?.address || '',
      city: org?.city || '',
      region: org?.region || '',
      country: org?.country
        ? { label: org.country, value: org.country.toLowerCase() }
        : null,
      website: org?.website ? org.website.replace(/^https?:\/\//i, '') : '',
      email: org?.email || '',
      description: org?.description || '',
    });
  }, [org, reset]);

  const onSubmit = async (data: FormValues) => {
    if (!isEdit) {
      setEdit(true);
      return;
    }

    if (!org?._id) {
      toast.error('Organization ID is missing');
      return;
    }

    let website = data.website;
    if (website && !/^https?:\/\//i.test(website)) {
      website = `https://${website}`;
    }

    const resp = await mutateAsync({
      ...data,
      country: data.country?.label,
      website,
    } as unknown as Organization);
    if (resp) {
      toast.success('Company profile updated successfully');
      setOrg(resp);
      setEdit(false);
    } else {
      toast.error('Failed to update company profile');
    }
  };

  const handleUpload = async (file: File | null, type: 'logo' | 'banner') => {
    if (!file) {
      toast.error(`No file selected for ${type}`);
      return;
    }
    if (!org?._id) {
      toast.error('Organization ID is missing');
    }

    const resp = await updateMedia(org?._id as string, {
      file,
      type,
    });

    if (resp) {
      toast.success(`Company ${type} updated successfully`);
      setOrg(resp);
      setEdit(false);
    } else {
      toast.error(`Failed to update company ${type}`);
    }
  };

  return (
    <FormProvider {...methods}>
      <OrgAdminProfileLayout
        saveChangeProps={{
          onClick: handleSubmit(onSubmit),
          disabled: isSubmitting || !isValid,
          isLoading: isSubmitting,
        }}
        onChangeFile={handleUpload}
        isEdit={isEdit}
      >
        {isEdit ? <CompanyForm /> : ''}
      </OrgAdminProfileLayout>
    </FormProvider>
  );
}
