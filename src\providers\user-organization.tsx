'use client';

import { useGetOrganizationById } from '@/api-requests/organization/get-org-by-id';
import { useParams } from 'next/navigation';

// TODO: Loading Skeleton
const OrgLoading = () => {
  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"></div>
    </div>
  );
};

const OrgNotFound = () => {
  return (
    <div className="flex h-screen flex-col items-center justify-center">
      <img src="/error/404.png" alt="Not found" />
    </div>
  );
};

export default function UserOrganizationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { id: orgId } = useParams();
  const { data, isFetched } = useGetOrganizationById(orgId as string);

  if (!isFetched) {
    return <OrgLoading />;
  }

  if (!data) {
    return <OrgNotFound />;
  }

  return <>{children}</>;
}
