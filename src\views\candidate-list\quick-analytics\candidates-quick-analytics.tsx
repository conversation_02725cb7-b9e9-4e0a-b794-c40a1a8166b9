'use client';

import { useGetOrgCandidatesQuickAnalytics } from '@/api-requests/job-candidate/get-candidates-quick-analytics';
import { SelectOption } from '@/api-requests/types';
import { CandidatesQuickAnalyticsSkeleton } from './loading';
import { RefreshCw } from 'lucide-react';
import { Tooltip } from 'rizzui/tooltip';
import { JSX } from 'react';

const formatNumber = (num = 0): string => {
  // Thoudsand: K
  // Million: M
  // Billion: B
  if (num >= 1_000_000_000) {
    return (num / 1_000_000_000).toFixed(1).replace(/\.0$/, '') + 'B';
  }
  if (num >= 1_000_000) {
    return (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M';
  }
  if (num >= 1_000) {
    return (num / 1_000).toFixed(1).replace(/\.0$/, '') + 'K';
  }
  return num.toString();
};

interface IProps {
  orgId: string;
}

const QuickAnalyticCard = ({
  title,
  content,
}: {
  title: string;
  content: string | JSX.Element;
}) => {
  return (
    <div className="flex flex-1 flex-col justify-between gap-4 rounded-xl bg-white p-4 shadow-md">
      <p className="text-sm text-gray-500">{title}</p>
      <p className="text-lg font-bold">{content}</p>
    </div>
  );
};

const mockData = [
  {
    title: 'Total Candidates',
    content: '100',
  },
  {
    title: 'Total Applications',
    content: '102',
  },
  {
    title: 'New Candidates (7d/30d)',
    content: '20/50',
  },
  {
    title: 'In Process Candidates (pending/screening/interview)',
    content: '19',
  },
];

export default function CandidatesQuickAnalytics({ orgId }: IProps) {
  const { data, isFetched, isLoading, error, refetch } =
    useGetOrgCandidatesQuickAnalytics({
      orgId,
    });

  if (!isFetched || isLoading) {
    return <CandidatesQuickAnalyticsSkeleton />;
  }

  if (error) {
    return (
      <div className="flex items-center gap-2 rounded-xl border border-red-200 bg-red-50 p-9 text-sm text-red-500">
        <span>Failed to load analytics.</span>
        <RefreshCw
          className="h-4 w-4 cursor-pointer"
          onClick={() => refetch()}
        />
      </div>
    );
  }

  const items = [
    {
      title: 'Total Candidates',
      content: (
        <Tooltip color="invert" content={data?.totalCandidates || 0}>
          <span>{formatNumber(data?.totalCandidates || 0)}</span>
        </Tooltip>
      ),
    },
    {
      title: 'Total Applications',
      content: (
        <Tooltip color="invert" content={data?.totalApplications || 0}>
          <span>{formatNumber(data?.totalApplications || 0)}</span>
        </Tooltip>
      ),
    },
    {
      title: 'New Candidates (7d/30d)',
      content: (
        <Tooltip
          color="invert"
          content={`${data?.last7DaysTotalCandidates || 0} / ${data?.last30DaysTotalCandidates || 0}`}
        >
          <span>{`${formatNumber(data?.last7DaysTotalCandidates || 0)} / ${formatNumber(data?.last30DaysTotalCandidates || 0)}`}</span>
        </Tooltip>
      ),
    },
    {
      title: 'In Process Candidates (pending/screening/interview)',
      content: (
        <Tooltip color="invert" content={data?.totalInProgressCandidates || 0}>
          <span>{formatNumber(data?.totalInProgressCandidates || 0)}</span>
        </Tooltip>
      ),
    },
  ];

  return (
    <div className="flex w-full flex-row flex-wrap gap-1 md:gap-6">
      {items.map((item: { title: string; content: string | JSX.Element }) => (
        <QuickAnalyticCard key={item.title} {...item} />
      ))}
    </div>
  );
}
