import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { cleanQueryParams } from '@/utils/url';
import { useQuery } from '@tanstack/react-query';
import { OrgCandidateListParams } from '../job-candidate/types';
import { ApiListResponse } from '../types';
import { ShortlistCandidate, ShortlistCandidateQueryKeys } from './types';

export async function getShortListCandidatesByOrg(
  orgId: string,
  shortlistId: string,
  params: OrgCandidateListParams
): Promise<ApiListResponse<ShortlistCandidate>> {
  const reps = await axiosInstance.get<ApiListResponse<ShortlistCandidate>>(
    API_ENDPONTS.GET_SHORTLIST_CANDIDATE_BY_ORG.replace(':orgId', orgId || ''),
    {
      params: cleanQueryParams({ ...params, shortlistId }),
    }
  );
  return reps.data;
}

export function useGetShortListCandidatesByOrg(
  orgId: string,
  shortlistId: string,
  params: OrgCandidateListParams
) {
  return useQuery<ApiListResponse<ShortlistCandidate>>({
    queryKey: [
      ShortlistCandidateQueryKeys.LIST_SHORTLIST_CANDIDATE_BY_ORG,
      params,
    ],
    queryFn: () => getShortListCandidatesByOrg(orgId, shortlistId, params),
    enabled: !!orgId && !!shortlistId,
  });
}
