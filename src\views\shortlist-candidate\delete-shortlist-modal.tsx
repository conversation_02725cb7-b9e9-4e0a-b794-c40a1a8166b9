import { <PERSON>Icon, But<PERSON>, Modal, Text, Title } from 'rizzu<PERSON>';
import CloseIcon from '../icons/close';

interface IProps {
  open: boolean;
  onClose: () => void;
  isLoading?: boolean;
  onDelete: () => void;
}

export default function DeleteShortlistModal({
  open,
  onClose,
  isLoading,
  onDelete,
}: IProps) {
  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div className="w-full p-6 sm:w-[450px]">
        <div className="mb-7 flex items-center justify-between">
          <Title as="h4">Delete Shortlisted Candidate</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-4">
          <div>
            <Text as="p" className="text-sm text-gray-700">
              Are you sure you want to delete the selected candidates from this
              shortlist? This action cannot be undone.
            </Text>
          </div>

          <div className="flex justify-end gap-3">
            <Button
              variant="outline"
              className="border-primary text-primary"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              className="bg-primary text-white"
              isLoading={isLoading}
              disabled={isLoading}
              onClick={onDelete}
            >
              Delete
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
