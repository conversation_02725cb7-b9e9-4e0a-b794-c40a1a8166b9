'use client';

import { useGetUserApplications } from '@/api-requests/user-profile/get-user-applications';
import { useWithdrawApplication } from '@/api-requests/user-profile/withdraw-application';
import cn from '@/utils/class-names';
import { safeFormatDate } from '@/utils/date';
import Pagination from '@/views/pagination';
import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { Badge, Button, Loader, Table, Tooltip } from 'rizzui';
import ConfirmWithdrawApplicationDialog from './confirm-withdraw-dialog';

const exampleApplications = [
  {
    id: 1,
    companyName: 'Barone LLC.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 1,
      level: 1,
      title: 'Software Engineer Simulation',
      point: 59,
      status: 'passed',
      minute: 15,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 86,
    duration: 'Beginner: 15 mins',
    companyLogoUrl: '/employer/avatar-theresa.jpeg',
    isHybrid: true,
  },
  {
    id: 2,
    companyName: 'Binford Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 2,
      level: 2,
      title: 'Software Engineer Simulation',
      point: 93,
      status: 'passed',
      minute: 30,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 79,
    duration: 'Intermediate: 30 mins',
    companyLogoUrl: '/employer/avatar-jacob.jpeg',
    isFullTime: true,
  },
  {
    id: 3,
    companyName: 'Biffco Enterprises Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 3,
      level: 3,
      title: 'Software Engineer Simulation',
      point: 45,
      status: 'passed',
      minute: 45,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 15,
    duration: 'Advanced: 45 mins',
    companyLogoUrl: '/employer/avatar-arlene.jpeg',
    isOnSite: true,
  },
  {
    id: 4,
    companyName: 'Adobe Inc',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 4,
      level: 1,
      title: 'Software Engineer Simulation',
      point: 93,
      status: 'passed',
      minute: 15,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 85,
    duration: 'Beginner: 15 mins',
    companyLogoUrl: '/employer/avatar-darrell.webp',
    hasContract: true,
  },
  {
    id: 5,
    companyName: 'Biffco Enterprises Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 5,
      level: 2,
      title: 'Software Engineer Simulation',
      point: 93,
      status: 'failed',
      minute: 30,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 86,
    duration: 'Intermediate: 30 mins',
    companyLogoUrl: '/employer/avatar-theresa.jpeg',
    isOnSite: true,
  },
  {
    id: 6,
    companyName: 'Big Kahuna Burger Ltd.',
    location: '345 Park Avenue, USA',
    postedTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    salary: '9,500 - 11,500',
    jobType: 'Full time',
    simulation: {
      id: 6,
      level: 3,
      title: 'Software Engineer Simulation',
      point: 43,
      status: 'inProgress',
      minute: 45,
    },
    categories: ['Technology'],
    aiReview: [
      'Strong technical background with 7+ years in full-stack and team leadership.',
      'Job Simulation score 84% shows solid, scalable coding.',
      'CV lacks measurable results and DevOps mention.',
      'Overall, a high-potential candidate with minor improvement areas.',
    ],
    status: 'Draft',
    aiVerdict: 'Strong in real-world delivery. Web3-native.',
    matches: 86,
    duration: 'Advanced: 45 mins',
    companyLogoUrl: '/employer/avatar-ralph.jpg',
    isFullTime: true,
  },
];

const badgeClassesApplicationStatus: Record<string, string> = {
  pending: 'bg-[#E8E8E8] text-[#161616]',
  rejected: 'bg-[#FFEADA] text-[#EE6C0F]',
  offer: 'bg-[#98FFDC] text-[#008457]',
  withdrawn: 'bg-[#0D1321] text-[#fff]',
};

const applicationStatusConvert = {
  pending: 'Pending',
  rejected: 'Rejected',
  offer: 'Offer',
  withdrawn: 'Withdrawn',
};

const APPLICATION_LIMIT = 10;

export default function JobApplied() {
  const [page, setPage] = useState(1);
  const [isOpenConfirmDialog, setIsOpenConfirmDialog] = useState(false);
  const [selectedApplicationId, setSelectedApplicationId] = useState<
    string | null
  >(null);

  const {
    data: applications,
    isLoading,
    refetch,
  } = useGetUserApplications({
    page,
    limit: APPLICATION_LIMIT,
  });

  const { mutateAsync: withdrawApplication, isPending } =
    useWithdrawApplication();

  const handleWithdrawApplication = async (id: string) => {
    try {
      await withdrawApplication({ id });
      setIsOpenConfirmDialog(false);
      setSelectedApplicationId(null);
      toast.success('Application withdrawn successfully.');
      refetch();
    } catch (error) {
      toast.error('Failed to withdraw application. Please try again later.');
    }
  };

  return (
    <>
      <div>
        <Table variant="modern">
          <Table.Header className="rounded-t-xl !bg-[#FAFAFA]">
            <Table.Row className="rounded-t-xl">
              <Table.Head>
                Job /<br />
                Simulation Info
              </Table.Head>
              <Table.Head>Applied Date</Table.Head>
              <Table.Head>Match</Table.Head>
              <Table.Head>Status</Table.Head>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {(applications?.data || []).map?.((application) => (
              <Table.Row key={application._id}>
                <Table.Cell>
                  <div className="space-y-3 lg:w-[400px]">
                    <div className="flex items-center gap-3">
                      <div>
                        {application?.org?.logo ||
                        application?.job?.companyLogoUrl ? (
                          <Image
                            src={
                              application?.org?.logo ||
                              application?.job?.companyLogoUrl ||
                              ''
                            }
                            alt={
                              application.org?.name ||
                              application.job?.companyName
                            }
                            width={60}
                            height={60}
                            className="h-15 w-15 rounded-full object-contain"
                            loader={({ src }) => src}
                          />
                        ) : (
                          <div
                            className="!h-15 !w-15 rounded-full bg-gray-100"
                            style={{ width: '60px', height: '60px' }}
                          />
                        )}
                      </div>

                      <div>
                        <div>
                          <p className="font-bold text-gray-700">
                            {application.job?.title ||
                              application.simulation.name}
                          </p>
                          <p className="text-gray-700">
                            <span className="text-sm">at </span>
                            {application.org?._id ? (
                              <Link
                                href={`/org/${application.org?._id}`}
                                target="_blank"
                                className="hover:underline"
                              >
                                <span className="font-bold">
                                  {application.org?.name ||
                                    application.job?.companyName}
                                </span>
                              </Link>
                            ) : (
                              <span className="font-bold">
                                {application.org?.name ||
                                  application.job?.companyName}
                              </span>
                            )}
                          </p>
                          {/* <p className="text-[12px] text-gray-500">
                          {application?.location} •{' '}
                          {safeFormatDistanceToNow(
                            new Date(application?.postedTime),
                            {
                              addSuffix: true,
                            }
                          )}
                        </p> */}
                        </div>

                        {/* <div className="flex flex-wrap items-center gap-2 text-[10px] text-gray-500">
                        <span className="flex items-center gap-1">
                          <MoneyIcon className="h-3 w-3" />
                          <span>{application?.salary || '-'}</span>
                        </span>
                        <span className="flex items-center gap-1">
                          <ClockIcon className="h-3 w-3" />
                          <span>
                            {getJobTypeString(application as never) || '-'}
                          </span>
                        </span>
                        <span className="flex items-center gap-1">
                          <TechnologyIcon className="h-3 w-3" />
                          {Array.isArray(application?.categories) &&
                          application?.categories?.length > 0 ? (
                            <span>{application.categories.join(', ')}</span>
                          ) : (
                            '-'
                          )}
                        </span>
                        {application?.simulation?.level && (
                          <span
                            className={`${levelStyle[simulationLevel[Number(application.simulation.level)]]} whitespace-nowrap rounded-full px-1`}
                          >
                            {
                              simulationLevel[
                                Number(application.simulation.level)
                              ]
                            }
                            : <b>{application.simulation.minute} mins</b>
                          </span>
                        )}
                      </div> */}
                      </div>
                    </div>

                    {/* <div>
                    <div>AI review</div>
                    <ul className="list-disc space-y-1 pl-5 text-sm text-gray-600">
                      {application.aiReview.map((review, idx) => (
                        <li key={idx}>{review}</li>
                      ))}
                    </ul>
                    <div>AI Verdict: {application.aiVerdict}</div>
                  </div> */}

                    <div className="flex gap-4">
                      {/* <Button
                      variant="outline"
                      className="rounded border border-[#00B074] px-3 py-1 text-sm text-black hover:bg-primary hover:text-white"
                    >
                      View detail
                    </Button> */}
                      {(application.applicationStatus || 'pending') ===
                        'pending' && (
                        <Button
                          variant="outline"
                          className="rounded border border-[#00B074] px-3 py-1 text-sm text-black hover:bg-primary hover:text-white"
                          onClick={() => {
                            setIsOpenConfirmDialog(true);
                            setSelectedApplicationId(application._id);
                          }}
                        >
                          Withdraw
                        </Button>
                      )}
                    </div>
                  </div>
                </Table.Cell>

                <Table.Cell>
                  <div className="flex flex-col">
                    <Tooltip
                      content={safeFormatDate(application.appliedAt, {
                        format: 'full',
                      })}
                      size="sm"
                      color="invert"
                    >
                      <span className="text-sm font-bold">
                        {safeFormatDate(application.appliedAt, {
                          format: 'long',
                        })}
                      </span>
                    </Tooltip>

                    {application.applicationStatus === 'withdrawn' &&
                      application.withdrawnAt && (
                        <Tooltip
                          content={safeFormatDate(application.withdrawnAt, {
                            format: 'full',
                          })}
                          size="sm"
                          color="invert"
                        >
                          <span className="text-xs text-gray-500">
                            {' '}
                            Withdrawn on{' '}
                            {safeFormatDate(application.withdrawnAt, {
                              format: 'long',
                            })}
                          </span>
                        </Tooltip>
                      )}
                  </div>
                </Table.Cell>

                <Table.Cell>
                  <span className="text-sm font-bold">
                    {application.matchPercentage || '-'}%
                  </span>
                </Table.Cell>

                <Table.Cell>
                  <Badge
                    variant="flat"
                    size="sm"
                    className={cn(
                      badgeClassesApplicationStatus[
                        (application.applicationStatus as string) || 'pending'
                      ],
                      'text-[12px] font-medium'
                    )}
                  >
                    {
                      applicationStatusConvert[
                        (application.applicationStatus as keyof typeof applicationStatusConvert) ??
                          'pending'
                      ]
                    }
                  </Badge>
                </Table.Cell>
              </Table.Row>
            ))}

            {isLoading ? (
              <Table.Row>
                <Table.Cell colSpan={7} className="h-40 text-center">
                  <div className="flex min-h-40 items-center justify-center">
                    <Loader className="h-8 w-8" />
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : (
              applications?.data?.length === 0 && (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center">
                    <div className="text-gray-500">No applications</div>
                  </Table.Cell>
                </Table.Row>
              )
            )}
          </Table.Body>
        </Table>

        <hr className="border-t border-gray-100 pt-4" />

        <Pagination
          total={applications?.meta?.total || 0}
          current={page}
          pageSize={APPLICATION_LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          variant="solid"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          // disabled={isLoading}
        />
      </div>
      {isOpenConfirmDialog && selectedApplicationId && (
        <ConfirmWithdrawApplicationDialog
          open={isOpenConfirmDialog}
          setOpen={() => {
            setIsOpenConfirmDialog(false);
            setSelectedApplicationId(null);
          }}
          onConfirm={() => {
            if (selectedApplicationId && !isPending) {
              handleWithdrawApplication(selectedApplicationId);
            }
          }}
          isLoading={isPending}
        />
      )}
    </>
  );
}
