import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ShortlistQueryKeys, UpdateParams, Shortlist } from './types';

export async function updateShortlist(
  payload: UpdateParams
): Promise<Shortlist> {
  const reps = await axiosInstance.put<Shortlist>(
    API_ENDPONTS.UPDATE_SHORTLIST.replace(':id', payload.id),
    payload
  );
  return reps.data;
}

export const useUpdateShortlist = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: UpdateParams) => updateShortlist(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShortlistQueryKeys.UPDATE_SHORTLIST],
      });
    },
  });
};
