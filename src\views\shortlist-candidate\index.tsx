'use client';

import { JobCandidate } from '@/api-requests/job-candidate/types';
import { Shortlist, useGetShorlistByOrg } from '@/api-requests/shortlist';
import {
  ShortlistCandidate as IShortlistCandidate,
  useCreateShortlistCandidate,
  useDeleteShortListCandidate,
  type ShortlistCandidate,
} from '@/api-requests/shortlist-candidate';
import { useDeleteShortListCandidateByIdAndOrg } from '@/api-requests/shortlist-candidate/delete-by-id-org';
import { useGetShortListCandidatesByOrg } from '@/api-requests/shortlist-candidate/get-by-org';
import { useCreateShortlist } from '@/api-requests/shortlist/create';
import { useDeleteShortlist } from '@/api-requests/shortlist/delete';
import { useUpdateShortlist } from '@/api-requests/shortlist/update';
import { useRouter } from 'next/navigation';
import { ApiListResponse, LIMIT, SelectOption } from '@/api-requests/types';
import { orgAtom } from '@/store/organization-atom';
import { userAtom } from '@/store/user-atom';
import { useAtom } from 'jotai';
import { debounce } from 'lodash';
import { useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { Button, Select } from 'rizzui';
import ArrowLeftIcon from '../icons/arrow-left';
import PlusIcon from '../icons/plus';
import CreateShortlistModal from '../shortlist/create-shortlist-modal';
import ShortlistModal from '../shortlist/shortlist-modal';
import ShortListCandidateFilter from './shortlist-candidate-filter';
import ShortlistCandidateTable from './shortlist-candidate-table';
import { exportToExcel } from '@/utils/export-to-excel';
import DeleteShortlistModal from './delete-shortlist-modal';
import { useBulkDeleteShortlistCandidate } from '@/api-requests/shortlist-candidate/bulk-delete';
import { API_DOMAINS } from '@/config/endpoint';

interface IProps {
  setView: (view: 'shortlist' | 'candidate') => void;
}

export default function ShortlistCandidate({ setView }: IProps) {
  const router = useRouter();
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState<SelectOption | null>(null);
  const [search, setSearch] = useState<string>('');
  const [candidateSeleted, setCandidateSeleted] =
    useState<IShortlistCandidate | null>(null);
  const [selectedShortListId, setSelectedShortListId] = useState<string | ''>(
    ''
  );
  const [openShortlist, setOpenShortlist] = useState(false);
  const [openCreateShortlist, setOpenCreateShortlist] = useState(false);
  const [selectedCandidates, setSelectedCandidates] = useState<
    ShortlistCandidate[]
  >([]);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);

  const searchRef = useRef<HTMLInputElement>(null!);

  const {
    data,
    isLoading,
    refetch: refreshShortlistCandidate,
  } = useGetShortListCandidatesByOrg(
    org?._id as string,
    selectedShortListId || '',
    {
      page,
      limit: LIMIT,
      name: search,
      status: status?.value as 'active' | 'completed',
    }
  );

  const { data: shortlists, refetch } = useGetShorlistByOrg(org?._id as string);
  const { mutateAsync, isPending: isDeleting } = useDeleteShortListCandidate();

  const { mutateAsync: createMutateAsync, isPending: isCreating } =
    useCreateShortlistCandidate();

  const {
    mutateAsync: createShortlistMutateAsync,
    isPending: creatingShortlist,
  } = useCreateShortlist();

  const {
    mutateAsync: deleteShortlistMutateAsync,
    isPending: deletingShortlist,
  } = useDeleteShortlist();

  const { mutateAsync: bulkDeleteMutateAsync, isPending: isBulkDeleting } =
    useBulkDeleteShortlistCandidate();

  const { mutateAsync: editShortlistMutateAsync, isPending: editingShortlist } =
    useUpdateShortlist();

  const {
    mutateAsync: deleteShortlistCandidateMutateAsync,
    isPending: deletingShortlistCandidate,
  } = useDeleteShortListCandidateByIdAndOrg();

  const handleChangeStatus = (option: SelectOption | null) => {
    setStatus(option);
    setPage(1);
  };

  const handleSearch = debounce((value: string) => {
    setSearch(value);
    setPage(1);
  }, 500);

  const handleDeleteShortlistCandidate = async (
    candidate: IShortlistCandidate
  ) => {
    const resp = await deleteShortlistCandidateMutateAsync({
      id: candidate._id,
      orgId: org?._id as string,
    });

    if (resp) {
      toast.success('Candidate removed from shortlist');
      refetch();
      refreshShortlistCandidate();
    } else {
      toast.error('Failed to remove candidate from shortlist');
    }
  };

  const handleClickAction = (
    action: string,
    candidate: IShortlistCandidate
  ) => {
    setCandidateSeleted(candidate);

    switch (action) {
      case 'Message':
        break;
      case 'Download CV':
        break;
      case 'View Detail':
        // TODO: pass current page, shortlistId
        router.push(`/org/admin/candidates/${candidate._id}?view=shortlist`);
        break;
      case 'Delete':
        handleDeleteShortlistCandidate(candidate);
        break;
      default:
        break;
    }
  };

  const handleCreateShortlistCandidate = async (
    list: Shortlist,
    candidate: JobCandidate,
    checked: boolean
  ) => {
    if (checked) {
      const resp = await createMutateAsync({
        candidateId: candidate?._id as string,
        shortlistId: list._id,
        orgId: org?._id as string,
      });

      if (resp) {
        toast.success('Candidate added to shortlist');
        refetch();
        setOpenShortlist(false);
      } else {
        toast.error('Failed to add candidate to shortlist');
      }
      return;
    } else {
      const resp = await mutateAsync({
        candidateId: candidate?._id as string,
        shortlistId: list._id,
        orgId: org?._id as string,
      });

      if (resp) {
        toast.success('Candidate removed from shortlist');
        refetch();
        setOpenShortlist(false);
      } else {
        toast.error('Failed to remove candidate from shortlist');
      }
      return;
    }
  };

  const handleCreateShortlist = async (
    name: string,
    candidate?: JobCandidate | null
  ) => {
    const resp = await createShortlistMutateAsync({
      candidateId: candidate?._id as string,
      orgId: org?._id as string,
      name,
    });

    if (resp) {
      toast.success('Shortlist created successfully');
      refetch();
      setOpenCreateShortlist(false);
    } else {
      toast.error('Failed to create shortlist');
    }
  };

  const handleEditShortlist = async (name: string, list: Shortlist) => {
    const resp = await editShortlistMutateAsync({
      id: list._id,
      name,
      orgId: org?._id as string,
    });

    if (resp) {
      toast.success('Shortlist updated successfully');
      refetch();
      refreshShortlistCandidate();
      return true;
    } else {
      toast.error('Failed to update shortlist');
      return false;
    }
  };

  const handleDeleteShortlist = async (list: Shortlist) => {
    const resp = await deleteShortlistMutateAsync({
      id: list._id,
      orgId: org?._id as string,
    });

    if (resp) {
      toast.success('Shortlist deleted successfully');
      refetch();
      refreshShortlistCandidate();
      return true;
    } else {
      toast.error('Failed to delete shortlist');
      return false;
    }
  };

  const handleBulkDeleteShortlistCandidate = async () => {
    const resp = await bulkDeleteMutateAsync({
      candidateIds: selectedCandidates.map((c) => c.candidateId),
      shortlistId: selectedShortListId,
      orgId: org?._id as string,
    });

    if (resp) {
      toast.success('Candidates removed from shortlist');
      refetch();
      setSelectedCandidates([]);
      setOpenDeleteModal(false);
      refreshShortlistCandidate();
      return true;
    } else {
      toast.error('Failed to remove candidates from shortlist');
      return false;
    }
  };

  const handleChangeShortlist = (option?: any) => {
    if (option?.value) {
      setSelectedShortListId(option.value);
      setSelectedCandidates([]);
      // refreshShortlistCandidate();
    }
  };

  useEffect(() => {
    if (selectedShortListId) {
      refreshShortlistCandidate();
    }
  }, [selectedShortListId]);

  useEffect(() => {
    if (shortlists && shortlists.length > 0 && !selectedShortListId) {
      setSelectedShortListId(shortlists[0]._id);
    }
  }, [shortlists, selectedShortListId]);

  const handleExportToExcel = () => {
    if (data?.data?.length === 0) {
      toast.error('No data to export');
      return;
    }
    exportToExcel(
      [
        'Candidate name',
        'Email',
        'Link profile',
        'Job name',
        'Link job',
        'Organization name',
        'Link organization',
        'Shortlist name',
        'Simulation name',
        'Link simulation',
        'Matches',
        'Status',
        'AI Review',
      ],
      data?.data.map((item) => [
        item.user?.firstName + ' ' + item.user?.lastName,
        item.user?.email,
        `${API_DOMAINS.BASE_URL}/profile/${item.user?.id}`,
        item.job?.title,
        item.job?.jobUrl,
        org?.name,
        `${API_DOMAINS.BASE_URL}/org/${org?._id}`,
        item.shortlist?.name,
        item.simulation?.name,
        `${API_DOMAINS.BASE_URL}/org/${org?._id}/sims/${item.simulation?._id}`,
        `${item.matchPercentage || 0}%`,
        item.status,
        item.aiEvaluation?.summary,
      ]) as string[][],
      {
        fileName: `Shortlist_Candidates`,
        noName: 'No',
      }
    );
  };

  const handleCheckboxChange = (
    checked: boolean,
    candidate: ShortlistCandidate | string
  ) => {
    if (candidate === 'all') {
      if (checked) {
        setSelectedCandidates(data?.data || []);
      } else {
        setSelectedCandidates([]);
      }
      return;
    } else {
      setSelectedCandidates((prev) => {
        if (checked) {
          return [...prev, candidate as ShortlistCandidate];
        } else {
          return prev.filter(
            (c) => c._id !== (candidate as ShortlistCandidate)._id
          );
        }
      });
    }
  };

  return (
    <>
      <div className="flex flex-col gap-4">
        <div>
          <Button variant="text" onClick={() => setView('candidate')}>
            <ArrowLeftIcon className="h-6 w-6" />
            <span className="pl-2">My Shortlist</span>
          </Button>
        </div>

        <div className="flex w-full items-center justify-between gap-4">
          <div className="w-full max-w-[300px]">
            {/* shortlists */}
            <Select
              options={(shortlists || []).map((list) => ({
                value: list._id,
                label: list.name,
              }))}
              placeholder="Select shortlist"
              value={
                !selectedShortListId
                  ? null
                  : shortlists?.find(
                      (list) => list._id === selectedShortListId
                    ) || null
              }
              onChange={handleChangeShortlist}
            />
          </div>
          <Button
            className="bg-primary text-white"
            onClick={() => setOpenShortlist(true)}
            size="sm"
          >
            <PlusIcon className="h-6 w-6" />
            <span>Create New Shortlist</span>
          </Button>
        </div>

        <div className="flex flex-col gap-4">
          <ShortListCandidateFilter
            searchProps={{
              onChange: (e) => handleSearch(e.target.value),
              ref: searchRef,
            }}
            statusProps={{
              value: status,
              onClear: () => setStatus(null),
              onChange: handleChangeStatus,
            }}
            selectedCandidates={selectedCandidates}
            totalCandidates={data?.data?.length || 0}
            onDeleteClick={() => setOpenDeleteModal(true)}
            onExportToExcel={handleExportToExcel}
          />
        </div>

        <ShortlistCandidateTable
          shortlistCandidateData={data as ApiListResponse<ShortlistCandidate>}
          isLoading={isLoading}
          page={page}
          setPage={setPage}
          onClickAction={handleClickAction}
          onCheckboxChange={handleCheckboxChange}
          selectedCandidates={selectedCandidates}
        />
      </div>

      {openShortlist && (
        <ShortlistModal
          open={openShortlist}
          onClose={() => setOpenShortlist(false)}
          shortlists={shortlists as Shortlist[]}
          onCheckboxChange={() => {}} //handleCreateShortlistCandidate
          candidate={candidateSeleted}
          isLoading={
            isDeleting || isCreating || editingShortlist || deletingShortlist
          }
          onCreate={() => {
            setOpenShortlist(false);
            setOpenCreateShortlist(true);
          }}
          canUpdate
          onEdit={handleEditShortlist}
          onDelete={handleDeleteShortlist}
        />
      )}

      {openCreateShortlist && (
        <CreateShortlistModal
          open={openCreateShortlist}
          onClose={() => setOpenCreateShortlist(false)}
          candidate={candidateSeleted}
          isLoading={creatingShortlist}
          onCreate={handleCreateShortlist}
        />
      )}

      {openDeleteModal && (
        <DeleteShortlistModal
          open={openDeleteModal}
          onClose={() => setOpenDeleteModal(false)}
          isLoading={isBulkDeleting}
          onDelete={handleBulkDeleteShortlistCandidate}
        />
      )}
    </>
  );
}
