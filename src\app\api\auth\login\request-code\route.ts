/* eslint-disable @typescript-eslint/no-explicit-any */
import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { data } = await axiosInstance.post(
      API_ENDPONTS.SIGN_IN_REQUEST_CODE,
      body
    );
    return NextResponse.json({ status: data?.sent });
  } catch (e: any) {
    return NextResponse.json(
      {
        message: e?.response?.data?.message || 'Something went wrong',
      },
      {
        status: e?.response?.data?.statusCode || 500,
      }
    );
  }
}
