import { formatDistanceToNow, isValid, parseISO } from 'date-fns';

/**
 * Safely format date to relative time string
 * @param dateInput - Date string, Date object, or timestamp
 * @param options - Optional formatting options
 * @returns Formatted date string or fallback
 */
export function safeFormatDistanceToNow(
  dateInput: string | Date | number | null | undefined,
  options: {
    addSuffix?: boolean;
    fallback?: string;
  } = {}
): string {
  const { addSuffix = true, fallback = '' } = options;

  try {
    // Handle null/undefined
    if (!dateInput) {
      return fallback;
    }

    let date: Date;

    // Convert input to Date object
    if (typeof dateInput === 'string') {
      // Try parsing ISO string first
      date = parseISO(dateInput);

      // If parseISO fails, try new Date()
      if (!isValid(date)) {
        date = new Date(dateInput);
      }
    } else if (typeof dateInput === 'number') {
      // Handle timestamp
      date = new Date(dateInput);
    } else if (dateInput instanceof Date) {
      date = dateInput;
    } else {
      return fallback;
    }

    // Validate the final Date object
    if (!isValid(date)) {
      console.warn(`Invalid date provided: ${dateInput}`);
      return fallback;
    }

    // Additional check for reasonable date range (optional)
    const currentYear = new Date().getFullYear();
    const dateYear = date.getFullYear();

    if (dateYear < 1900 || dateYear > currentYear + 10) {
      console.warn(`Date out of reasonable range: ${dateInput}`);
      return fallback;
    }

    return formatDistanceToNow(date, { addSuffix });
  } catch (error) {
    console.error('Error formatting date:', error, 'Input:', dateInput);
    return fallback;
  }
}

/**
 * Check if a date input is valid
 * @param dateInput - Date input to validate
 * @returns boolean indicating if date is valid
 */
export function isValidDateInput(
  dateInput: string | Date | number | null | undefined
): boolean {
  try {
    if (!dateInput) return false;

    let date: Date;

    if (typeof dateInput === 'string') {
      date = parseISO(dateInput);
      if (!isValid(date)) {
        date = new Date(dateInput);
      }
    } else if (typeof dateInput === 'number') {
      date = new Date(dateInput);
    } else if (dateInput instanceof Date) {
      date = dateInput;
    } else {
      return false;
    }

    return isValid(date);
  } catch {
    return false;
  }
}

/**
 * Format date with multiple fallback options
 * @param dateInput - Date input
 * @param formatOptions - Formatting options
 * @returns Formatted date or fallback
 */
export function safeFormatDate(
  dateInput: string | Date | number | null | undefined,
  formatOptions: {
    format?: 'relative' | 'short' | 'long' | 'full';
    fallback?: string;
    addSuffix?: boolean;
  } = {}
): string {
  const { format = 'long', fallback = 'N/A', addSuffix = true } = formatOptions;

  if (!isValidDateInput(dateInput)) {
    return fallback;
  }

  try {
    const date = new Date(dateInput!);

    switch (format) {
      case 'relative':
        return formatDistanceToNow(date, { addSuffix });
      case 'short':
        return date.toLocaleDateString();
      case 'long':
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      case 'full':
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
      default:
        return formatDistanceToNow(date, { addSuffix });
    }
  } catch (error) {
    console.error('Error in safeFormatDate:', error);
    return fallback;
  }
}
