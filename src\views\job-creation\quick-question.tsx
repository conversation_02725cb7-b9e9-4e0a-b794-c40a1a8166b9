import React, { useEffect, useMemo, useState } from 'react';
import PlusIcon from '../icons/plus';
import DeleteIcon from '../icons/delete';
import { ActionIcon, Button, Checkbox, Input, Select, Text } from 'rizzui';
import Field<PERSON>abel from './field-label';
import { JobQuickQuestion, QuestionType } from '@/api-requests/job';
import { Controller, useFormContext } from 'react-hook-form';
import { FormValues } from '.';
import { SelectOption } from '@/api-requests/types';

const questionOptions = [
  { label: 'Yes/No', value: QuestionType.YES_NO },
  { label: 'Single Choice', value: QuestionType.SINGLE_CHOICE },
  { label: 'Multiple Choice', value: QuestionType.MULTI_CHOICE },
  { label: 'Numer', value: QuestionType.NUMBER },
  { label: 'Short Text', value: QuestionType.SHORT_TEXT },
  { label: 'Long Text', value: QuestionType.LONG_TEXT },
];

export default function QuickQuestion() {
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useFormContext<FormValues>();

  const quickQuestions = watch('quickQuestions');

  const addQuestion = () => {
    setValue('quickQuestions', [
      ...quickQuestions,
      {
        id: crypto.randomUUID(),
        type: { label: 'Short Text', value: QuestionType.SHORT_TEXT },
        text: '',
        required: false,
        options: [],
      },
    ]);
  };

  return (
    <div className="mx-auto w-full max-w-6xl">
      {/* Toolbar */}
      <div className="mb-6 flex flex-wrap items-center justify-between gap-4">
        <div className="font-bold">Quick Questions</div>
        <Button
          onClick={addQuestion}
          className="bg-primary text-white"
          size="sm"
        >
          <PlusIcon /> <span>Add Question</span>
        </Button>
      </div>

      {/* Content */}
      <div className="grid grid-cols-1 gap-6">
        <div className="space-y-4">
          {quickQuestions.map((q, i) => (
            <QuestionEditor
              key={q.id}
              q={
                q as JobQuickQuestion & {
                  type: { label: string; value: string };
                }
              }
              idx={i}
              control={control}
              setValue={setValue}
              quickQuestions={
                quickQuestions as (JobQuickQuestion & {
                  type: { label: string; value: string };
                })[]
              }
              errors={errors}
            />
          ))}
          {quickQuestions.length === 0 && (
            <div className="rounded-2xl border border-dashed border-slate-300 p-8 text-center text-slate-600">
              No questions yet. Click{' '}
              <span className="font-semibold">Add Question</span> to get
              started.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// --- Sub Components -----------------------------------------------------
type QEProps = {
  q: JobQuickQuestion & { type: { label: string; value: string } };
  idx: number;
  control: any;
  setValue: any;
  quickQuestions: (JobQuickQuestion & {
    type: { label: string; value: string };
  })[];
  errors: any;
};

function QuestionEditor({
  q,
  idx,
  control,
  setValue,
  quickQuestions,
  errors,
}: QEProps) {
  return (
    <div className="rounded-md border p-4">
      <div className="mb-3 flex items-center justify-between gap-4">
        <span className="rounded-lg bg-slate-100 px-2 py-1 font-semibold">
          #{idx + 1}
        </span>
        <div className="flex w-full items-start gap-4">
          <div className='flex-1'>
            <Controller
              name={`quickQuestions.${idx}.text`}
              control={control}
              rules={{
                required: 'Please enter question content',
              }}
              render={({ field }) => (
                <Input
                  {...field}
                  variant="flat"
                  placeholder="Question content"
                  className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                  inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                />
              )}
            />
            {errors.quickQuestions?.[idx]?.text && (
              <Text as="p" className="mt-0.5 text-xs text-red-600">
                {errors.quickQuestions[idx].text.message}
              </Text>
            )}
          </div>
          <div className='pt-1'>
            <ActionIcon
              variant="outline"
              size="sm"
              className="border-primary text-primary hover:bg-primary hover:text-white"
              onClick={() => {
                setValue(
                  'quickQuestions',
                  quickQuestions.filter((_, i) => i !== idx)
                );
              }}
            >
              <DeleteIcon className="mx-auto h-4 w-4" />
            </ActionIcon>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 items-end gap-4 sm:grid-cols-2">
        <div>
          <FieldLabel title="Question Type" />
          <Controller
            name={`quickQuestions.${idx}.type`}
            control={control}
            rules={{
              required: 'Please select question type',
            }}
            render={({ field }) => (
              <Select
                {...field}
                value={field.value || null}
                onChange={(val: SelectOption) => {
                  field.onChange(val);
                  if (
                    val?.value === QuestionType.SINGLE_CHOICE ||
                    val?.value === QuestionType.MULTI_CHOICE
                  ) {
                    setValue(
                      `quickQuestions.${idx}.options`,
                      q.options?.length
                        ? q.options
                        : [{ id: crypto.randomUUID(), value: '' }]
                    );
                  } else {
                    setValue(`quickQuestions.${idx}.options`, []);
                  }
                }}
                clearable
                onClear={() => field.onChange(null)}
                options={questionOptions}
                placeholder="Select pay type"
                className="w-full [&_.rizzui-select-button]:!rounded-lg [&_.rizzui-select-button]:!border-0 [&_.rizzui-select-button]:!bg-white [&_.rizzui-select-button]:!px-4 [&_.rizzui-select-button]:!shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_.rizzui-select-button]:!outline-0 [&_.rizzui-select-button]:!ring-0 [&_.rizzui-select-button]:focus:!ring-0"
              />
            )}
          />
          {errors.quickQuestions?.[idx]?.type && (
            <Text as="p" className="mt-0.5 text-xs text-red-600">
              {errors.quickQuestions[idx].type.message}
            </Text>
          )}
        </div>
        <div className="pb-2">
          <Controller
            name={`quickQuestions.${idx}.required`}
            control={control}
            rules={{
              required: false,
            }}
            render={({ field }) => (
              <Checkbox
                {...field}
                variant="flat"
                size="sm"
                checked={field.value}
                onChange={(e) => field.onChange(e.target.checked)}
                label={'Required'}
              />
            )}
          />
        </div>
      </div>

      <div className="mt-3 grid grid-cols-1 gap-4">
        {(q.type?.value === QuestionType.SHORT_TEXT ||
          q.type?.value === QuestionType.LONG_TEXT) && (
          <div className="grid grid-cols-2 gap-3">
            <div>
              <FieldLabel title="Min length" />
              <Controller
                name={`quickQuestions.${idx}.minLength`}
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    min={0}
                    value={field.value ?? 0}
                    onChange={(e) => {
                      const val = e.target.value || 0;
                      if (val === '' || /^\d+$/.test(val as string)) {
                        field.onChange(val);
                      }
                    }}
                    variant="flat"
                    className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
            </div>
            <div>
              <FieldLabel title="Max length" />
              <Controller
                name={`quickQuestions.${idx}.maxLength`}
                control={control}
                rules={{
                  required: false,
                }}
                render={({ field }) => (
                  <Input
                    {...field}
                    min={1}
                    value={field.value ?? 150}
                    onChange={(e) => {
                      const val = e.target.value || 0;
                      if (val === '' || /^\d+$/.test(val as string)) {
                        field.onChange(val);
                      }
                    }}
                    variant="flat"
                    className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                    inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                  />
                )}
              />
            </div>
          </div>
        )}
      </div>

      {(q.type?.value === QuestionType.SINGLE_CHOICE ||
        q.type?.value === QuestionType.MULTI_CHOICE) && (
        <div className="mt-4 rounded-xl bg-slate-50 p-3">
          <div className="mb-2 flex items-center justify-between">
            <p className="text-sm font-semibold">Options</p>
            <Button
              onClick={() => {
                setValue(`quickQuestions.${idx}.options`, [
                  ...(q.options ?? []),
                  { id: crypto.randomUUID(), value: '' },
                ]);
              }}
              variant="outline"
              size="sm"
              className="border-primary text-primary hover:bg-primary hover:text-white"
            >
              <PlusIcon /> <span>Add option</span>
            </Button>
          </div>
          <div className="space-y-2">
            {(q.options ?? []).map((op, oi) => (
              <div key={op.id} className="flex items-start gap-4">
                <div className="flex-1">
                  <Controller
                    name={`quickQuestions.${idx}.options.${oi}.value`}
                    control={control}
                    rules={{
                      required: 'Please enter option text',
                    }}
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder="Option text"
                        variant="flat"
                        className="w-full rounded-lg shadow-[0_4px_30px_rgba(0,0,0,0.15)] [&_*]:!border-0 [&_*]:!border-none [&_*]:!outline-none [&_*]:!ring-0"
                        inputClassName="rounded-lg !border-0 p-0 px-4 outline-none ring-0 focus:ring-0 placeholder:text-gray-400"
                      />
                    )}
                  />
                  {errors.quickQuestions?.[idx]?.options?.[oi]?.value && (
                    <Text as="p" className="mt-0.5 text-xs text-red-600">
                      {errors.quickQuestions[idx].options[oi].value.message}
                    </Text>
                  )}
                </div>
                <div className="pt-1">
                  <ActionIcon
                    variant="outline"
                    size="sm"
                    className="border-primary text-primary hover:bg-primary hover:text-white"
                    onClick={() => {
                      setValue(`quickQuestions.${idx}.options`, [
                        ...(q.options ?? []).filter((_, idx) => idx !== oi),
                      ]);
                    }}
                    disabled={(q.options ?? []).length <= 1}
                  >
                    <DeleteIcon className="mx-auto h-4 w-4" />
                  </ActionIcon>
                </div>
              </div>
            ))}
            {(q.options ?? []).length === 0 && (
              <div className="rounded-lg border border-dashed p-4 text-center text-xs text-slate-600">
                No options available
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
