import { ShortlistCandidate } from '../shortlist-candidate';

export const ShortlistQueryKeys = {
  LIST_SHORTLIST_BY_ORG: 'listShortlistByOrg',
  CREATE_SHORTLIST: 'createShortlist',
  UPDATE_SHORTLIST: 'updateShortlist',
  DELETE_SHORTLIST: 'deleteShortlist',
};

export interface CreateParams {
  candidateId: string;
  orgId: string;
  name: string;
}
export interface UpdateParams {
  id: string;
  orgId: string;
  name: string;
}

export interface Shortlist {
  _id: string;
  orgId: string;
  name: string;
  shortlistCandidates?: ShortlistCandidate[];
}
