'use client';

import Image from 'next/image';
import cn from '@/utils/class-names';

const items = [
  {
    title: 'Better Quality Hires',
    image: '/employer/better-quantity-hires.png',
    description:
      'Evaluate candidates based on actual job performance rather than interview skills. See how they solve real problems and handle work scenarios.',
  },
  {
    title: 'Faster Screening Process',
    image: '/employer/faster-screening-process.png',
    description:
      'Skill lengthy CV reviews and phone screenings. Candidates complete simulations upfront, so you only interview pre-qualified talent.',
  },
  {
    title: 'Data-Driven Decisions',
    image: '/employer/data-driven-decisions.png',
    description:
      'Make hiring decisions backed by objective performance data. Compare candidates fairly using standardized assessment criteria.',
  },
  {
    title: 'Reduce Hiring Costs',
    image: '/employer/reduce-hiring-costs.png',
    description:
      'Lower turnover rates and reduce time-to-hire. Find candidates who are genuinely suited for the role and excited about the work.',
  },
  {
    title: 'Access Motivated Talent',
    image: '/employer/access-motivated-talent.png',
    description:
      'Attract candidates who are confident in their abilities and prefer to demonstrate skill over traditional application processes.',
  },
  {
    title: 'Diverse Hiring',
    image: '/employer/diverse-hiring.png',
    description:
      'Remove unconscious bias from early screening. Focus on what candidates can do rather than where they’ve worked or studied.',
  },
];

export default function EmployerSimulationBased() {
  return (
    <section>
      <h2 className="mb-4 text-center text-2xl font-bold md:text-3xl">
        Why Employers Choose Simulation-Based Hiring
      </h2>
      <p className="mb-12 text-center text-gray-600">
        See candidates in action before making your decision. <br />
        Our platform connects you with talent ready to prove their abilities.
      </p>

      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        {items.map((item, index) => {
          const isLastCol = (index + 1) % 3 === 0;
          const isLastRow = index >= 3;
          return (
            <div
              key={index}
              className={cn(
                'flex flex-col items-center gap-4 border-gray-200 px-6 py-10 text-center',
                !isLastCol ? 'border-r' : '',
                !isLastRow ? 'border-b' : ''
              )}
            >
              <Image
                src={item.image}
                alt={item.title}
                width={160}
                height={160}
                className="h-40 w-40 object-contain"
                // unoptimized={true}
                loader={({ src }) => src}
              />
              <h3 className="text-lg font-semibold">{item.title}</h3>
              <p className="text-sm leading-relaxed text-gray-600">
                {item.description}
              </p>
            </div>
          );
        })}
      </div>
    </section>
  );
}
