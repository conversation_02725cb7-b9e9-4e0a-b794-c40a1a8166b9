'use client';

import { Button } from 'rizzui';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

export default function AllJobsBanner() {
  const router = useRouter();

  const handleCreateJob = () => {
    router.push('/org/admin/jobs/create');
  };

  return (
    <section
      // className="bg-cover bg-center bg-no-repeat"
      // style={{ backgroundImage: 'url("/job/job-hero-bg.png")' }}
    >
      <Button
        className="rounded-full bg-primary text-white"
        onClick={handleCreateJob}
      >
        Create a New Job
      </Button>
    </section>
  );
}
