'use client';

import { Role } from '@/api-requests/user/types';
import { useAuthActions } from '@/hooks/use-auth-actions';
import { resetFiltersAtom, resetSearchAtom } from '@/store/job-atom';
import { searchModeAtom } from '@/store/talent-atom';
import { userAtom } from '@/store/user-atom';
import cn from '@/utils/class-names';
import SignInModal from '@/views/auth/sign-in-up/sign-in-modal';
import MessageIcon from '@/views/icons/message';
import NotificationIcon from '@/views/icons/notification';
import { useAtom } from 'jotai';
import { Menu } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useState } from 'react';
import { Avatar, Button, Dropdown } from 'rizzui';
import logoImage from '../../../public/ic-io-logo-light.png';

interface IProps {
  onOpenMenu?: () => void;
  isNew?: boolean;
}

export default function Header({ onOpenMenu, isNew = false }: IProps) {
  const pathname = usePathname();

  const [, resetFilters] = useAtom(resetFiltersAtom);
  const [, resetSearch] = useAtom(resetSearchAtom);
  const [, setSearchMode] = useAtom(searchModeAtom);
  const [user] = useAtom(userAtom);

  const { logout } = useAuthActions();

  const [openLoginModal, setOpenLoginModal] = useState(false);

  const navItems = [
    // { label: 'Dashboard', href: '/employer/admin/dashboard' },
    { label: 'All Jobs', href: '/employer/admin/jobs' },
    { label: 'All Candidates', href: '/employer/admin/candidates' },
    { label: 'Search Talent', href: '/employer/admin/search-talent' },
  ];

  const handleLogoClick = () => {
    resetFilters();
    resetSearch();
  };

  const handleClickLink = () => {
    setSearchMode(false);
  };

  return (
    <>
      <header className="sticky top-0 z-[100] flex h-[80px] items-center bg-white px-4 shadow-md xl:px-0">
        <div className="mx-auto flex w-full items-center justify-between px-4 lg:px-6">
          {/* Logo */}
          <div className="flex items-center gap-6">
            <div className="flex items-center gap-4">
              {!!onOpenMenu && (
                <Menu
                  onClick={onOpenMenu}
                  className="cursor-pointer lg:hidden"
                />
              )}
              <Link onClick={handleLogoClick} href="/">
                <div className="flex items-center space-x-2">
                  <Image
                    src={logoImage}
                    alt="Industry Connect Logo"
                    width={0}
                    height={32}
                    className="h-6 w-auto lg:h-8"
                    loader={({ src }) => src}
                  />
                </div>
              </Link>
              <Link onClick={handleLogoClick} href="/employer/landing">
                <div className="hover:cursor-pointer">Employer</div>
              </Link>
            </div>

            {/* Menu */}
            {!isNew && (
              <nav className="hidden h-full items-center space-x-8 text-sm font-medium text-gray-600 md:flex">
                {navItems.map((item) => {
                  const isActive = pathname === item.href;
                  return (
                    <Link
                      key={item.href}
                      href={item.href}
                      onClick={handleClickLink}
                    >
                      <span
                        className={cn(
                          'relative cursor-pointer text-[#484848] transition-colors',
                          isActive
                            ? 'font-bold text-primary'
                            : 'hover:text-primary'
                        )}
                      >
                        {item.label}
                        {isActive && (
                          <span className="absolute -bottom-[32px] left-0 h-[3px] w-full bg-primary" />
                        )}
                      </span>
                    </Link>
                  );
                })}
              </nav>
            )}
          </div>

          {/* Sign In Button */}
          {user && user.role === Role.EMPLOYER ? (
            <div className="flex items-center gap-4">
              {/* Post Job button */}
              {!isNew && (
                <>
                  <Button className="rounded-full bg-primary text-white">
                    Post Job
                  </Button>
                  <div className="h-6 w-px bg-gray-300" />
                </>
              )}

              {/* Message and Notification */}
              <div className="flex items-center gap-4">
                <div className="relative">
                  <MessageIcon />
                  <span className="absolute right-0 top-0 h-2 w-2 -translate-y-[25%] rounded-full bg-primary" />
                </div>

                <div className="relative">
                  <NotificationIcon />
                  <span className="absolute right-0 top-0 h-2 w-2 -translate-y-[25%] rounded-full bg-primary" />
                </div>
              </div>

              {/* Separator */}
              <div className="h-6 w-px bg-gray-300" />

              {/* Avatar */}
              <div className="flex h-full items-center space-x-4">
                <Dropdown>
                  <Dropdown.Trigger>
                    <Avatar
                      name={`${user.firstName} ${user.lastName}`}
                      src={user.avatar}
                      size="md"
                      className="h-10 w-10 cursor-pointer rounded-full object-cover"
                    />
                  </Dropdown.Trigger>
                  <Dropdown.Menu className="w-fit divide-y">
                    <Dropdown.Item className="hover:bg-primary hover:text-white">
                      {/* <Link href="/profile">Profile</Link> */}
                      Profile
                    </Dropdown.Item>
                    <Dropdown.Item
                      className="hover:bg-primary hover:text-white"
                      onClick={() => logout()}
                    >
                      Logout
                    </Dropdown.Item>
                  </Dropdown.Menu>
                </Dropdown>
              </div>
            </div>
          ) : (
            <Button
              className="bg-primary text-white"
              onClick={() => setOpenLoginModal(true)}
            >
              Sign In
            </Button>
          )}
        </div>
      </header>

      {openLoginModal && (
        <SignInModal
          open={openLoginModal}
          onClose={() => setOpenLoginModal(false)}
          role={Role.EMPLOYER}
        />
      )}
    </>
  );
}
