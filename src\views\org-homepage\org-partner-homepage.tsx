'use client';

import { Organization } from '@/api-requests/organization/types';
import {
  HomeSimulation,
  useGetOrgFeaturedSimulations,
  useGetOrgHomeSimulationsInfinite,
} from '@/api-requests/simulation';
import SimParticipants from '@/components/SimParticipants';
import StartSimulationButton from '@/components/StartSimulationButton';
import { orgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';
import Link from 'next/link';
import { useEffect, useRef, useState } from 'react';

const SimulationLibrary = () => {
  const loaderRef = useRef<HTMLDivElement | null>(null);
  const [keyword, _setKeyword] = useState('');

  const [org] = useAtom(orgAtom);

  const { simulations: featuredSimulations } = useGetOrgFeaturedSimulations({
    orgId: org?._id || '',
  });

  const { simulations, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetOrgHomeSimulationsInfinite({ orgId: org?._id || '', keyword });

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting) {
          console.log('Loader is intersecting');
        }
        if (
          target.isIntersecting &&
          !isFetchingNextPage &&
          org?._id &&
          hasNextPage
        ) {
          fetchNextPage();
        }
      },
      {
        root: null,
        rootMargin: '0px',
        threshold: 1.0,
      }
    );

    if (loaderRef.current) {
      observer.observe(loaderRef.current);
    }

    return () => {
      if (loaderRef.current) {
        observer.unobserve(loaderRef.current);
      }
    };
  }, [hasNextPage, isFetchingNextPage, org?._id]);

  return (
    <div className="space-y-10">
      <div>
        <h2 className="mb-4 text-sm font-semibold">Featured Simulations</h2>

        {!simulations?.length ? (
          <div className="text-center text-gray-500">No simulations found.</div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <SimulationList simulations={featuredSimulations} org={org} />
          </div>
        )}
      </div>
      <div>
        <h2 className="mb-4 text-sm font-semibold">Simulation Library</h2>

        {!simulations?.length ? (
          <div className="text-center text-gray-500">No simulations found.</div>
        ) : (
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
            <SimulationList simulations={simulations} org={org} />
          </div>
        )}

        {/* Sentinel element */}
        <div ref={loaderRef} className="flex h-12 items-center justify-center">
          {isFetchingNextPage && (
            <div className="h-6 w-6 animate-spin rounded-full border-2 border-gray-300 border-t-blue-500"></div>
          )}
        </div>
      </div>
    </div>
  );
};

const SimulationList = ({
  simulations,
  org,
}: {
  simulations: HomeSimulation[];
  org: Organization | null;
}) => {
  return (
    <>
      {simulations.map((sim) => (
        <div
          key={sim.id}
          className="flex flex-col rounded-xl bg-white p-4 shadow"
        >
          <img
            src={sim.banner || '/simulation/default-banner.png'}
            alt={sim.name}
            className="h-36 w-full rounded-md object-cover"
          />

          {/* Info */}
          <h3 className="mt-3 text-[16px] font-semibold">{sim.name}</h3>
          <SimParticipants
            candidates={sim.candidates || []}
            total={sim.joinedCount || 0}
          />

          <p className="mt-2 line-clamp-3 flex-1 text-sm text-gray-600">
            {sim.description}
          </p>

          <hr className="my-5" />

          {/* Action */}
          <div className="flex items-center justify-between text-sm">
            {/* <button className="text-[#0D1321] hover:underline">
                View detail
              </button> */}
            <Link
              href={`${org?._id}/sims/${sim.simulationId}`}
              className="text-[#0D1321] hover:underline"
            >
              View detail
            </Link>
            {sim.progress?.status === 'completed' ? (
              <>Completed</>
            ) : (
              <StartSimulationButton
                simId={sim.simulationId}
                buttonProps={{
                  className: 'border-[#0D1321] text-[#0D1321] hover:bg-gray-50',
                  variant: 'outline',
                }}
              >
                {sim.progress?.status === 'active'
                  ? 'Resume'
                  : 'Start Simulation'}
              </StartSimulationButton>
            )}
          </div>
        </div>
      ))}
    </>
  );
};

export default function UserOrgPartnerHomepage() {
  return <SimulationLibrary />;
}
