import React from 'react';

export default function HowItWorks() {
  const steps = [
    {
      body: 'You choose a role and complete a short, realistic task.',
      icon: (
        // Document icon
        <svg
          viewBox="0 0 24 24"
          className="h-6 w-6"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.8"
        >
          <rect x="5" y="3" width="12" height="18" rx="2" />
          <path d="M9 7h6" />
          <path d="M9 11h6" />
          <path d="M9 15h3" />
          <path d="M17 8l2 2-2 2" />
        </svg>
      ),
    },
    {
      body: 'Our AI scores your work and explains your strengths clearly.',
      icon: (
        // Brain/circuit icon
        <svg
          viewBox="0 0 24 24"
          className="h-6 w-6"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.8"
        >
          <path d="M8.5 6.5a3 3 0 0 1 6 0c2.5 0 4 1.8 4 4 0 2-1.5 3.5-3.5 3.5 0 2-1.5 3.5-3.5 3.5S8 16 8 14c-2 0-3.5-1.5-3.5-3.5s1.2-3.5 4-4Z" />
          <path d="M12 7v3m0 4v3M9 10h2m2 0h2M9 14h2m2 0h2" />
        </svg>
      ),
    },
    {
      body: 'You share your proof with a hiring manager and move faster.',
      icon: (
        // Briefcase on notebook icon
        <svg
          viewBox="0 0 24 24"
          className="h-6 w-6"
          fill="none"
          stroke="currentColor"
          strokeWidth="1.8"
        >
          <rect x="4" y="3" width="14" height="18" rx="2" />
          <path d="M9 7V6a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1" />
          <rect x="7" y="10" width="8" height="6" rx="1" />
          <path d="M11 10v2m2-2v2" />
          <path d="M20 7v10" />
        </svg>
      ),
    },
  ];

  return (
    <div>
      <div className="text-xl font-semibold tracking-tight">How It Works</div>

      <div className="mx-auto w-full max-w-[1350px]">
        <div className="mt-10 grid grid-cols-1 gap-x-6 md:gap-x-14 lg:gap-x-20 gap-y-12 md:grid-cols-3">
          {steps.map((s, i) => (
            <div key={i}>
              <div className="mb-4 flex h-8 w-8 items-center justify-center rounded-lg bg-slate-100 ring-1 ring-slate-200 text-gray-500">
                {s.icon}
              </div>
              <div className="mb-2 text-sm text-slate-400">Step {i + 1}</div>
              <p className='max-w-[300px]'>{s.body}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
