'use client';

import Image from 'next/image';
import React from 'react';
import logoImage from '../../../../public/ic-io-logo-light.png';

export default function JobFooter() {
  return (
    <footer className="w-full">
      <div className="mx-auto max-w-[1440px] px-4 py-8 sm:px-6 lg:py-12 xl:px-0">
        <div className="grid grid-cols-1 items-start gap-10 lg:grid-cols-[1.25fr_1fr_1fr]">
          {/* Left block: logo + copyright */}
          <div>
            <div className="flex items-center">
              <Image
                src={logoImage}
                alt="IndustryConnect.IO"
                width={155}
                height={32}
                className="h-auto w-[155px]"
                loader={({ src }) => src}
              />
            </div>
            <p className="mt-6 text-sm text-slate-400">
              © 2025 Talent Project. All Rights Reserved.
            </p>
          </div>

          {/* Explore */}
          <nav aria-label="Explore" className="space-y-4">
            <h3 className="font-semibold">Explore</h3>
            <ul className="space-y-2">
              {[
                { label: 'Junior Data Analyst', href: '#' },
                { label: 'QA Tester (Manual)', href: '#' },
                { label: 'Business Intelligence Intern', href: '#' },
              ].map((l) => (
                <li key={l.label}>
                  <a
                    href={'#'}
                    className="text-base text-slate-400 transition hover:text-slate-600"
                  >
                    {l.label}
                  </a>
                </li>
              ))}
            </ul>
          </nav>

          {/* Company */}
          <nav aria-label="Company" className="space-y-4">
            <h3 className="font-semibold">Company</h3>
            <ul className="space-y-2">
              {[
                { label: 'Terms', href: '#' },
                { label: 'Privacy', href: '#' },
                { label: 'Support', href: '#' },
              ].map((l) => (
                <li key={l.label}>
                  <a
                    href={'#'}
                    className="text-base text-slate-400 transition hover:text-slate-600"
                  >
                    {l.label}
                  </a>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      </div>
    </footer>
  );
}
