import React from 'react';

function PlusSquareIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="56"
      height="56"
      viewBox="0 0 56 56"
      {...props}
    >
      <path
        fill="currentColor"
        d="M9.637 41.945h4.359v4.032c0 4.828 2.438 7.265 7.36 7.265h25.03c4.876 0 7.337-2.437 7.337-7.265V21.063c0-4.805-2.461-7.243-7.336-7.243h-4.36v-3.797c0-4.828-2.46-7.265-7.36-7.265H9.638c-4.899 0-7.36 2.437-7.36 7.265v24.68c0 4.828 2.461 7.242 7.36 7.242m.07-3.773c-2.344 0-3.656-1.242-3.656-3.68V10.234c0-2.437 1.312-3.703 3.656-3.703h24.914c2.297 0 3.633 1.266 3.633 3.703v3.586H21.356c-4.922 0-7.36 2.414-7.36 7.243v17.109Zm11.695 11.297c-2.343 0-3.633-1.266-3.633-3.703V21.273c0-2.437 1.29-3.68 3.633-3.68h24.914c2.32 0 3.633 1.243 3.633 3.68V45.79c0 2.414-1.312 3.68-3.633 3.68Zm12.516-6.188c.984 0 1.758-.797 1.758-1.851v-6.094h5.953c1.101 0 1.992-.844 1.992-1.852c0-.984-.89-1.828-1.992-1.828h-5.953v-6.07c0-1.055-.774-1.828-1.758-1.828s-1.781.773-1.781 1.828v6.07h-6.164c-.985 0-1.875.89-1.875 1.828c0 .985.89 1.852 1.875 1.852h6.164v6.094c0 1.054.797 1.851 1.78 1.851"
      />
    </svg>
  );
}

export default PlusSquareIcon;
