'use client';

import { org<PERSON>tom } from '@/store/organization-atom';
import { useAtom } from 'jotai';
import { Mail, MapPin, Globe, CameraIcon } from 'lucide-react';
import { ActionIcon, Button } from 'rizzui';

interface OrgUserHomeLayoutProps {
  children: React.ReactNode;
}

export default function OrgUserHomeLayout(props: OrgUserHomeLayoutProps) {
  const { children } = props;

  const [org] = useAtom(orgAtom);

  return (
    <>
      <div className="mx-auto overflow-hidden">
        <div className="relative">
          {/* {org?.banner ? (
            <img
              src={org.banner}
              alt="Banner"
              className="h-[300px] w-full overflow-hidden bg-white object-cover"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling;
                if (fallback) {
                  (fallback as HTMLElement).style.display = 'block';
                }
              }}
            />
          ) : (
            <div className={`h-[300px] w-full rounded-lg bg-primary`} />
          )} */}

          <img
            src={org?.banner || '/org/default-banner.png'}
            alt="Banner"
            className="h-[300px] w-full overflow-hidden bg-white object-cover rounded-lg"
            onError={(e) => {
              e.currentTarget.style.display = 'none';
              const fallback = e.currentTarget.nextElementSibling;
              if (fallback) {
                (fallback as HTMLElement).style.display = 'block';
              }
            }}
          />

          <div className="absolute -bottom-20 left-1/2 w-fit -translate-x-1/2 transform items-center gap-4 rounded-full bg-white p-0.5 xl:left-5 xl:-translate-x-0 xl:transform-none">
            <div className="relative h-[180px] w-[180px]">
              {org?.logo ? (
                <img
                  src={org?.logo}
                  alt="Logo"
                  className="h-full w-full overflow-hidden rounded-full border-4 border-[#3E5C76] bg-white shadow-md"
                />
              ) : (
                <div className="h-full w-full rounded-full border-4 border-[#3E5C76] bg-primary" />
              )}
            </div>
          </div>
        </div>

        <div className="mt-20 p-5 xl:mt-0">
          <div className="xl:ml-52">
            <div className="flex items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold">{org?.name}</h1>
                <div className="mt-1 flex flex-wrap items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <MapPin size={16} />
                    <span>{org?.location}</span>
                  </div>
                  {org?.website && (
                    <div className="flex items-center gap-1">
                      <Globe size={16} />
                      <a
                        href={org.website}
                        className="text-blue-600 hover:underline"
                        target="_blank"
                      >
                        {org.website}
                      </a>
                    </div>
                  )}
                  {org?.email && (
                    <div className="flex items-center gap-1">
                      <Mail size={16} />
                      <a
                        href={`mailto:${org.email}`}
                        className="text-blue-600 hover:underline"
                      >
                        {org.email}
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {org?.description && (
          <div className="mt-5 rounded-lg bg-white p-5">
            <p className="mb-2 font-semibold text-gray-500">
              Short Description:
            </p>
            {/* TODO: add show more button */}
            <p className="leading-relaxed">{org.description}</p>
          </div>
        )}
      </div>
      <div className="mt-10">{children}</div>
    </>
  );
}
