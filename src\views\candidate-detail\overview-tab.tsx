import { JobCandidate } from '@/api-requests/job-candidate/types';
import cn from '@/utils/class-names';
import { ShieldAlert } from 'lucide-react';
import { Badge, Tooltip } from 'rizzui';

interface IProps {
  candidate: JobCandidate;
}

// Star rating component
const StarRating = ({
  rating,
  maxRating = 5,
}: {
  rating: number;
  maxRating?: number;
}) => {
  return (
    <div className="flex items-center gap-1">
      {Array.from({ length: maxRating }, (_, index) => (
        <svg
          key={index}
          className={`h-4 w-4 ${
            index < rating ? 'fill-current text-yellow-400' : 'text-gray-300'
          }`}
          viewBox="0 0 20 20"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
    </div>
  );
};

export default function OverviewTab({ candidate }: IProps) {
  return (
    <div className="space-y-8">
      {/* Score Cards */}
      <div className={cn('grid grid-cols-1 gap-6 md:grid-cols-3')}>
        {/* Overall Score */}
        {candidate.applyMode === 'simulation' && (
          <div className="rounded-lg border bg-white p-6 shadow-sm">
            <div className="mb-2 text-sm text-gray-500">Overall Score</div>
            <div className="text-3xl font-bold text-gray-900">
              {candidate.scores ?? '-'}%
            </div>
          </div>
        )}

        {/* Match */}
        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <div className="mb-2 text-sm text-gray-500">Match</div>
          <div className="text-3xl font-bold text-gray-900">
            {candidate.matchPercentage ?? '-'}%
          </div>
        </div>

        {/* Signals */}
        <div
          className={cn(
            'rounded-lg border bg-white p-6 shadow-sm',
            candidate.applyMode === 'cv' ? 'col-span-2' : ''
          )}
        >
          <div className="mb-2 text-sm text-gray-500">Signals</div>
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-900">
              Risk & Duplicates
            </div>
            <div className="space-y-1">
              {(candidate?.aiEvaluation?.risks || []).map((risk) => (
                <div className="flex items-center gap-2 text-sm">
                  {/* <div className="h-2 w-2 rounded-full bg-orange-400"></div> */}
                  <ShieldAlert
                    className={cn(
                      'h-4 w-4 text-yellow-600',
                      risk.level === 'medium' ? 'text-orange-600' : '',
                      risk.level === 'high' ? 'text-red-600' : ''
                    )}
                  />
                  <Tooltip color="invert" content={risk.mitigation || ''}>
                    <span>
                      {risk.name}: {risk.explanation}
                    </span>
                  </Tooltip>
                </div>
              ))}
            </div>
            {/* <div className="mt-3 flex items-center justify-between">
              <span className="text-sm text-gray-500">Possible duplicates</span>
              <Badge variant="outline" className="text-xs">
                pending
              </Badge>
            </div> */}
          </div>
        </div>
      </div>

      {/* Strengths and Weaknesses */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <h3 className="mb-4 text-lg font-semibold text-gray-900">
            Strengths
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            Top signals from Simulation/CV analysis
          </p>
          <ul className="space-y-3">
            {!!candidate.aiEvaluation?.strengths?.length &&
              candidate.aiEvaluation.strengths.map((item, idx) => (
                <li key={idx} className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary"></div>
                  <span className="text-sm text-gray-700">
                    <span className="font-bold">{item.point}</span>:{' '}
                    {item.evidence}
                  </span>
                </li>
              ))}
          </ul>
        </div>

        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <h3 className="mb-4 text-lg font-semibold text-gray-900">
            Weaknesses
          </h3>
          <p className="mb-4 text-sm text-gray-500">
            Areas to probe in interview
          </p>
          <ul className="space-y-3">
            {!!candidate.aiEvaluation?.areasForImprovement?.length &&
              candidate.aiEvaluation.areasForImprovement.map((item, idx) => (
                <li key={idx} className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 flex-shrink-0 rounded-full bg-primary"></div>
                  <span className="text-sm text-gray-700">
                    <span className="font-bold">{item.point}</span>:{' '}
                    {item.evidence}
                  </span>
                </li>
              ))}
          </ul>
        </div>
      </div>

      {/* Skills Assessment */}
      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <h3 className="mb-4 text-lg font-semibold text-gray-900">
          Skills Assessment
        </h3>
        <p className="mb-6 text-sm text-gray-500">
          Structured competency ratings
        </p>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          {/* Hard Skills */}
          <div>
            <h4 className="mb-4 text-sm font-semibold text-gray-900">
              Hard skills
            </h4>
            <div className="space-y-4">
              {candidate.aiEvaluation?.skills?.hardSkills?.length ? (
                candidate.aiEvaluation.skills.hardSkills.map((skill, idx) => (
                  <div key={idx} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">{skill.name}</span>
                    <StarRating rating={skill.rating} />
                  </div>
                ))
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">JavaScript</span>
                    <StarRating rating={4} />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">MongoDB</span>
                    <StarRating rating={4} />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">
                      Queues/Systems
                    </span>
                    <StarRating rating={4} />
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Soft Skills */}
          <div>
            <h4 className="mb-4 text-sm font-semibold text-gray-900">
              Soft skills
            </h4>
            <div className="space-y-4">
              {candidate.aiEvaluation?.skills?.softSkills?.length ? (
                candidate.aiEvaluation.skills.softSkills.map((skill, idx) => (
                  <div key={idx} className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">{skill.name}</span>
                    <StarRating rating={skill.rating} />
                  </div>
                ))
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">Communication</span>
                    <StarRating rating={5} />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">
                      Problem Solving
                    </span>
                    <StarRating rating={4} />
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-700">
                      Attention to Detail
                    </span>
                    <StarRating rating={4} />
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {!!candidate.quickQuestions?.length && (
        <div className="rounded-lg border bg-white p-6 shadow-sm">
          <h3 className="mb-4 text-lg font-semibold text-gray-900">
            Quick Questions
          </h3>
          {/* <p className="mb-6 text-sm text-gray-500">
            Submissions from Simulation
          </p> */}

          <div className="space-y-4 pl-4">
            {candidate.quickQuestions.map((question, idx) => (
              <div key={question.id}>
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{question.text}</h4>
                </div>
                <p className="mb-2 text-sm text-gray-600">{question.answer}</p>
                {/* {question.submission?.content && (
                  <div className="rounded bg-gray-50 p-3 text-sm text-gray-700">
                    <p className="mb-1 font-medium">Submission:</p>
                    <p className="whitespace-pre-wrap">
                      {question.submission.content}
                    </p>
                  </div>
                )} */}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Task Results */}
      {/* <div className="rounded-lg border bg-white p-6 shadow-sm">
        <h3 className="mb-4 text-lg font-semibold text-gray-900">
          Task Results
        </h3>
        <p className="mb-6 text-sm text-gray-500">
          Submissions from Simulation
        </p>

        <div className="space-y-4">
          {candidate.tasks?.length ? (
            candidate.tasks.map((task, idx) => (
              <div key={idx} className="border-l-4 border-blue-500 pl-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{task.title}</h4>
                  <Badge
                    variant="solid"
                    className="bg-gray-800 text-xs text-white"
                  >
                    {Math.floor(Math.random() * 5) + 1} pass
                  </Badge>
                </div>
                <p className="mb-2 text-sm text-gray-600">{task.description}</p>
                {task.submission?.content && (
                  <div className="rounded bg-gray-50 p-3 text-sm text-gray-700">
                    <p className="mb-1 font-medium">Submission:</p>
                    <p className="whitespace-pre-wrap">
                      {task.submission.content}
                    </p>
                  </div>
                )}
              </div>
            ))
          ) : (
            <>
              <div className="border-l-4 border-blue-500 pl-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">
                    Fix a Simple JavaScript Bug
                  </h4>
                  <Badge
                    variant="solid"
                    className="bg-gray-800 text-xs text-white"
                  >
                    2 pass
                  </Badge>
                </div>
              </div>

              <div className="border-l-4 border-blue-500 pl-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">
                    Implement a MongoDB Query for User Data
                  </h4>
                  <Badge
                    variant="solid"
                    className="bg-gray-800 text-xs text-white"
                  >
                    3 pass
                  </Badge>
                </div>
              </div>

              <div className="border-l-4 border-blue-500 pl-4">
                <div className="mb-2 flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">
                    Design a Queue System for Order Processing
                  </h4>
                  <Badge variant="outline" className="text-xs">
                    partial
                  </Badge>
                </div>
              </div>
            </>
          )}
        </div>
      </div> */}
    </div>
  );
}
