'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Title } from 'rizzu<PERSON>';
import CloseIcon from '../icons/close';

interface IProps {
  open: boolean;
  onClose: () => void;
  onPublish?: () => void;
  isLoading?: boolean;
}

export default function ConfirmPublishModal({
  open,
  onClose,
  onPublish,
  isLoading,
}: IProps) {
  return (
    <Modal isOpen={open} onClose={onClose} size="lg" customSize={'450px'}>
      <div className="w-full rounded-[20px] p-6 sm:w-[450px]">
        <div className="mb-5 flex items-center justify-between">
          <Title as="h4">Confirm Publish Job</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-6">
          <div>
            <div className="font-semibold">
              Are you sure you want to publish this job?
            </div>
            <div className="space-y-2">
              <p className="text-sm text-gray-500">
                Once published, the job will be live and visible to all users.
              </p>
              <p className="text-sm font-medium text-amber-600">
                Note: After publishing, you will not be able to edit this job
                posting. However, you can unpublish it or disable application mode.
              </p>
            </div>
          </div>

          <div className="mt-6 flex justify-end space-x-4">
            <Button
              variant="outline"
              className="border-primary text-primary hover:bg-primary hover:text-white"
              onClick={onClose}
            >
              Cancel
            </Button>
            <Button
              className="bg-primary text-white"
              onClick={onPublish}
              isLoading={isLoading}
              disabled={isLoading}
            >
              Publish
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
