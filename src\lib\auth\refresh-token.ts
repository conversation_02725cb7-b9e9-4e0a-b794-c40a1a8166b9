import {
  AT_COOKIE,
  isProd,
  maxAgeDays,
  RT_COOKIE,
} from '@/app/api/auth/_utils';
import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { decodeToken } from '@/utils/token';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function refreshToken(
  request: NextRequest,
  response: NextResponse
) {
  try {
    const at = request.cookies.get(AT_COOKIE)?.value;
    const rt = request.cookies.get(RT_COOKIE)?.value as string;
    const tokenDecoded = decodeToken(at || '');

    if (
      (!at ||
        (tokenDecoded?.expire &&
          tokenDecoded?.expire * 1000 - Date.now() <= 5 * 60 * 1000)) &&
      rt
    ) {
      const { data } = await axiosInstance.post(
        API_ENDPONTS.REFRESH_TOKEN,
        {},
        { headers: { Authorization: `Bearer ${rt}` } }
      );

      if (data?.user) {
        response.cookies.set(AT_COOKIE, data.accessToken, {
          httpOnly: false,
          secure: isProd,
          sameSite: 'lax',
          path: '/',
          maxAge: maxAgeDays(1),
        });

        response.cookies.set(RT_COOKIE, data.refreshToken, {
          httpOnly: true,
          secure: isProd,
          sameSite: 'lax',
          path: '/',
          maxAge: maxAgeDays(7),
        });
      }
      return data;
    }

    return null;
  } catch (error) {
    console.error('Refresh failed:', error);
    return null;
  }
}
