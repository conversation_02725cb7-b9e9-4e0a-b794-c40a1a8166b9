'use client';

import Header from '@/layouts/org-user-layout/header';

interface OrgUserLayoutProps {
  children: React.ReactNode;
}

const ProfileBackground = () => {
  return (
    <section
      className="absolute left-0 top-20 z-0 h-60 w-full bg-cover bg-center bg-no-repeat"
      style={{ backgroundImage: 'url("/job/job-hero-bg.png")' }}
    ></section>
  );
};

export default function OrgUserLayout(props: OrgUserLayoutProps) {
  const { children } = props;

  return (
    <main className="relative min-h-screen bg-[#F8F8F8]">
      <Header />

      <ProfileBackground />

      <div className="relative mx-auto w-full max-w-[1200px] px-4 py-[60px] xl:px-0">
        {children}
      </div>
    </main>
  );
}
