import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';
import { useQuery } from '@tanstack/react-query';
import { Organization, OrganizationQueryKeys } from './types';
import { setOrgAtom } from '@/store/organization-atom';
import { useAtom } from 'jotai';

export async function getOrganizationByOwner(
  ownerId: string
): Promise<Organization> {
  const response = await axiosInstance.get<Organization>(
    API_ENDPONTS.GET_ORG_BY_OWNER.replace(':ownerId', ownerId)
  );
  return response.data;
}

export function useGetOrganizationByOwner(ownerId: string) {
  const [, setOrg] = useAtom(setOrgAtom);

  return useQuery<Organization>({
    queryKey: [OrganizationQueryKeys.GET_ORG_BY_OWNER, ownerId],
    queryFn: () => getOrganizationByOwner(ownerId),
    enabled: !!ownerId,
    select: (data) => {
      setOrg(data);
      return data;
    },
  });
}
