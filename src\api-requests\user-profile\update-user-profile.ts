import axiosInstance from '@/utils/http-client';
import {
  UpdateUserProfileParams,
  UserProfile,
  UserProfileQueryKeys,
} from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function updateUserProfile(
  params: UpdateUserProfileParams
): Promise<UserProfile> {
  const response = await axiosInstance.post<UserProfile>(
    API_ENDPONTS.UPSERT_USER_PROFILE,
    params,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response.data;
}

export const useUpdateUserProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UpdateUserProfileParams) => updateUserProfile(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [UserProfileQueryKeys.UPSERT_USER_PROFILE],
      });
    },
  });
};
