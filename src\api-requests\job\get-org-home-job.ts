import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { JobQueryKeys, PublicJobDetail } from './types';

import { useQuery } from '@tanstack/react-query';

export async function getOrgPublicJob(params: {
  orgId: string;
  jobId: string;
}): Promise<PublicJobDetail> {
  const { orgId, jobId } = params;
  const response = await requestGet<PublicJobDetail>(
    API_ENDPONTS.GET_ORG_PUBLIC_JOB,
    {},
    { orgId, jobId }
  );
  return response.data;
}

export function useGetOrgPublicJob(params: { orgId: string; jobId: string }) {
  return useQuery<PublicJobDetail>({
    queryKey: [JobQueryKeys.GET_ORG_PUBLIC_JOB, params.orgId, params.jobId],
    queryFn: () => getOrgPublicJob(params),
    enabled: !!params.orgId && !!params.jobId,
  });
}
