'use client';

import {
  AdminSimulation,
  useGetOrgAdminSimulations,
} from '@/api-requests/simulation';
import { ApiListResponse, LIMIT } from '@/api-requests/types';
import { orgAtom } from '@/store/organization-atom';
import { userAtom } from '@/store/user-atom';
import { useAtom } from 'jotai';
import { debounce } from 'lodash';
import { useRef, useState } from 'react';
import { Button } from 'rizzui/button';
import CreateJobSimulationModal from './create-simulation-dialog';
import OrgAdminSimulationsFilter from './filter';
import OrgAdminSimulationsTable from './table';

export interface Option {
  label: string;
  value: string;
}

export default function OrgAdminSimulationManagement() {
  const [user] = useAtom(userAtom);
  const [org] = useAtom(orgAtom);
  const [page, setPage] = useState(1);
  const [status, setStatus] = useState<Option | null>(null);
  const [keyword, setKeyword] = useState<string>('');
  const searchRef = useRef<HTMLInputElement>(null!);

  const [openCreateSimModal, setOpenCreateSimModal] = useState(false);

  const { data, isLoading, refetch } = useGetOrgAdminSimulations({
    orgId: org?._id || '',
    page,
    limit: LIMIT,
    keyword: keyword,
    status: status?.value,
  });

  const handleSearch = debounce((value: string) => {
    if (!value || value.trim().length > 2) {
      setKeyword(value);
      setPage(1);
    }
  }, 500);

  const onCreateSimulationSuccess = () => {
    if (page === 1) refetch();
  };

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-wrap items-center justify-between">
          <p className="text-xl font-bold">Simulation Library</p>
          <Button
            className="text-white"
            variant="solid"
            onClick={() => setOpenCreateSimModal(true)}
          >
            Create Simulation
          </Button>
        </div>
        <OrgAdminSimulationsFilter
          searchProps={{
            // value: search,
            onChange: (e) => handleSearch(e.target.value),
            ref: searchRef,
          }}
        />

        <OrgAdminSimulationsTable
          data={data as ApiListResponse<AdminSimulation>}
          isLoading={isLoading}
          page={page}
          setPage={setPage}
          refetch={refetch}
        />
      </div>
      <CreateJobSimulationModal
        open={openCreateSimModal}
        setOpen={setOpenCreateSimModal}
        onSuccess={onCreateSimulationSuccess}
      />
    </>
  );
}
