import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { InteractionQueryKeys, UnFavoriteJobsParams } from './types';

export async function unfavoriteJobs(
  params: UnFavoriteJobsParams
): Promise<boolean> {
  const response = await axiosInstance.post<boolean>(
    API_ENDPONTS.USER_UNFAVORITE_JOBS,
    params
  );
  return response.data;
}

export const useUnfavoriteJobs = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: UnFavoriteJobsParams) => unfavoriteJobs(params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [InteractionQueryKeys.USER_UNFAVORITE_JOBS],
      });
    },
  });
};
