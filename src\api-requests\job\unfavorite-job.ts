import axiosInstance from '@/utils/http-client';
import { Job, JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Interaction } from '../interaction';

export async function unfavoriteJob(jobId: string): Promise<boolean> {
  const response = await axiosInstance.post<boolean>(
    API_ENDPONTS.UNFAVORITE_JOB.replace(':jobId', jobId)
  );
  return response.data;
}

export const useUnfavoriteJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (jobId: string) => unfavoriteJob(jobId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.UNFAVORITE_JOB],
      });
    },
  });
};
