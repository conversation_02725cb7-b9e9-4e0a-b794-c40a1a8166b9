'use client';

import { useCreateOrgSimulation } from '@/api-requests/simulation/create-org-simulations';
import { orgAtom } from '@/store/organization-atom';
import cn from '@/utils/class-names';
import { useAtom } from 'jotai';
import { CircleX } from 'lucide-react';
import { useEffect } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { Button, Input, Loader, Modal, Textarea } from 'rizzui';

interface IProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  initialData?: FormValues | null;
  mode?: 'create' | 'edit';
  onSuccess?: () => void;
}

type Task = {
  title: string;
  content: string;
};

type FormValues = {
  name: string;
  description: string;
  tasks: Task[];
};

export default function CreateJobSimulationModal({
  open,
  setOpen,
  initialData,
  mode = 'create',
  onSuccess,
}: IProps) {
  const [org] = useAtom(orgAtom);

  const { mutateAsync: createOrgSimulation, isPending } =
    useCreateOrgSimulation();

  const { register, control, handleSubmit, reset, watch } = useForm<FormValues>(
    {
      defaultValues: {
        name: '',
        description: '',
        tasks: [{ title: '', content: '' }],
      },
    }
  );

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'tasks',
  });

  useEffect(() => {
    if (open) {
      if (initialData) {
        reset(initialData);
      } else {
        reset({
          name: '',
          description: '',
          tasks: [{ title: '', content: '' }],
        });
      }
    }
  }, [open, initialData, reset]);

  const onSubmit = async (data: FormValues) => {
    try {
      if (isPending) return;
      if (mode === 'edit') {
        console.log('Update Job Simulation:', data);
      } else {
        console.log('Create Job Simulation:', data);
        await createOrgSimulation({
          orgId: org?._id || '',
          name: data.name,
          description: data.description,
          tasks: data.tasks.map((task) => ({
            title: task.title,
            content: task.content,
          })),
        });
        onSuccess?.();
      }
      setOpen(false);
      reset();
    } catch (error) {
      toast.error(
        `Failed to ${mode === 'edit' ? 'update' : 'create'} simulation`
      );
    }
  };

  return (
    <Modal
      isOpen={open}
      onClose={() => null}
      containerClassName="md:w-[1080px] md:max-w-[1080px]"
    >
      <div className="p-6">
        <h2 className="mb-2 text-lg font-semibold">
          {mode === 'edit' ? 'Edit Job Simulation' : 'Create Job Simulation'}
        </h2>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Job Simulation Name */}
          <Input
            label="Job Simulation Name"
            placeholder="Enter Job Simulation Name"
            disabled={isPending}
            {...register('name', { required: true })}
          />

          {/* Description */}
          <Textarea
            label="Description"
            placeholder="Enter description"
            disabled={isPending}
            {...register('description')}
          />

          {/* Tasks */}
          <div>
            <label className="mb-2 block text-sm font-medium">Tasks</label>
            <div className="space-y-6">
              {fields.map((field, index) => (
                <div
                  key={field.id}
                  className="relative space-y-3 rounded-lg border p-4"
                >
                  <Input
                    label={`Task Title #${index + 1}`}
                    placeholder="Enter task title"
                    disabled={isPending}
                    {...register(`tasks.${index}.title` as const, {
                      required: true,
                    })}
                  />
                  <Textarea
                    label="Task Content"
                    placeholder="Enter task content"
                    disabled={isPending}
                    {...register(`tasks.${index}.content` as const, {
                      required: true,
                    })}
                  />
                  {fields.length > 1 && (
                    <CircleX
                      onClick={() => {
                        if (!isPending) remove(index);
                      }}
                      className="absolute right-2 top-0 cursor-pointer text-gray-500 hover:text-red-500"
                    />
                  )}
                </div>
              ))}
              <Button
                type="button"
                variant="outline"
                disabled={isPending}
                onClick={() => append({ title: '', content: '' })}
              >
                + Add Task
              </Button>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              disabled={isPending}
              onClick={() => setOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="solid"
              className={cn('text-white', isPending && 'text-primary')}
              disabled={isPending}
            >
              {isPending ? (
                <Loader className="h-4 w-4" />
              ) : mode === 'edit' ? (
                'Update'
              ) : (
                'Save'
              )}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
