'use client';

import { MoreHorizontalIcon } from 'lucide-react';
import { Avatar, Badge, Checkbox, Dropdown, Loader, Table } from 'rizzui';
import MessageIcon from '../icons/message';
import DownloadCvIcon from '../icons/download-cv';
import HeartOutlineIcon from '../icons/heart-outline';
import EditSquareIcon from '../icons/edit-square';
import Pagination from '../pagination';
import { useEffect, useState } from 'react';
import CandidateFilter from './candidate-filter';
import { useGetCandidatesByJob } from '@/api-requests/job-candidate/get-candidate-by-job';
import { LIMIT } from '@/api-requests/types';
import { useRouter } from 'next/navigation';
import { ShortlistCandidate } from '@/api-requests/shortlist-candidate';

const badgeClasses: Record<string, string> = {
  active: 'bg-[#FFE2C0] text-[#CD6B01]',
  completed: 'bg-[#FFD8D8] text-[#B90707]',
};

interface IProps {
  jobId?: string;
  setCandidateInfo?: (info: {
    totalCandidates: number;
    matchedCandidates: number;
    averageMatch: number;
  }) => void;
}

export default function CandidateListTab({ jobId, setCandidateInfo }: IProps) {
  const router = useRouter();
  const [page, setPage] = useState(1);

  const { data: candidateData, isLoading } = useGetCandidatesByJob({
    jobId,
    page,
    limit: LIMIT,
  });

  useEffect(() => {
    if (setCandidateInfo && candidateData) {
      setCandidateInfo({
        totalCandidates: candidateData?.meta.total || 0,
        matchedCandidates: candidateData?.meta.total
          ? candidateData?.data?.filter(
              (c) => c.matchPercentage && c.matchPercentage >= 70
            ).length
          : 0,
        averageMatch: candidateData?.meta.total
          ? Math.round(
              candidateData?.data?.reduce(
                (sum, c) => sum + (c.matchPercentage || 0),
                0
              ) / candidateData?.meta.total
            )
          : 0,
      });
    }
  }, [candidateData]);

  const actions = [
    {
      icon: <MessageIcon className="h-5 w-5" />,
      label: 'Message',
    },
    {
      icon: <DownloadCvIcon className="h-5 w-5" />,
      label: 'Download CV',
    },
    {
      icon: <EditSquareIcon className="h-5 w-5" />,
      label: 'View Detail',
      onClick: (c: ShortlistCandidate) => router.push('/candidates/' + c._id),
    },
  ];

  return (
    <div className="space-y-4">
      <CandidateFilter />

      <div className="rounded-xl bg-white pb-5 shadow-lg">
        <Table>
          <Table.Header className="rounded-t-xl !bg-[#FAFAFA]">
            <Table.Row>
              {/* <Table.Head className="w-4"> </Table.Head> */}
              <Table.Head className="w-[20%]">Candidate name</Table.Head>
              <Table.Head className="w-[100px]">Matches</Table.Head>
              <Table.Head className="w-[20%]">Simulation</Table.Head>
              <Table.Head className="w-full">AI Review</Table.Head>
              <Table.Head className="!text-right">Actions</Table.Head>
            </Table.Row>
          </Table.Header>

          <Table.Body>
            {candidateData?.data?.map((candidate, index) => (
              <Table.Row key={candidate._id}>
                {/* <Table.Cell>
   <Checkbox variant="flat" size="sm" />
 </Table.Cell> */}

                <Table.Cell>
                  <div className="flex items-center gap-3">
                    <Avatar
                      src={candidate.user?.avatar || '/avatar/user-default.png'}
                      name={
                        candidate.user?.firstName +
                        ' ' +
                        candidate.user?.lastName
                      }
                      customSize={40}
                      className="!bg-transparent"
                    />
                    <div className="text-left">
                      <div className="font-medium text-gray-900">
                        {candidate.user?.firstName +
                          ' ' +
                          candidate.user?.lastName}
                      </div>
                    </div>
                  </div>
                </Table.Cell>

                <Table.Cell>
                  <span className="font-semibold text-gray-800">
                    {candidate.matchPercentage || 0}%
                  </span>
                </Table.Cell>

                <Table.Cell>
                  <div>{candidate.simulation?.name}</div>
                  <Badge
                    variant="flat"
                    size="sm"
                    className={badgeClasses[candidate.status]}
                  >
                    {candidate.status}
                  </Badge>
                </Table.Cell>

                <Table.Cell>
                  <div>{candidate.aiEvaluation?.summary || '-'}</div>
                </Table.Cell>

                <Table.Cell className="text-right">
                  <div className="flex justify-end gap-3">
                    <Dropdown placement="bottom-end">
                      <Dropdown.Trigger>
                        <MoreHorizontalIcon />
                      </Dropdown.Trigger>
                      <Dropdown.Menu className="w-fit divide-y">
                        {actions.map((action, idx) => (
                          <Dropdown.Item
                            key={idx}
                            className="hover:bg-primary hover:text-white"
                            onClick={() => action.onClick?.(candidate)}
                          >
                            <div className="flex items-center">
                              {action.icon}
                              <span className="ml-2">{action.label}</span>
                            </div>
                          </Dropdown.Item>
                        ))}
                      </Dropdown.Menu>
                    </Dropdown>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}

            {isLoading ? (
              <Table.Row>
                <Table.Cell colSpan={7} className="h-40 text-center">
                  <div className="flex min-h-40 items-center justify-center">
                    <Loader className="h-8 w-8" />
                  </div>
                </Table.Cell>
              </Table.Row>
            ) : (
              candidateData?.data?.length === 0 && (
                <Table.Row>
                  <Table.Cell colSpan={7} className="text-center">
                    <div className="text-gray-500">No candidates found</div>
                  </Table.Cell>
                </Table.Row>
              )
            )}
          </Table.Body>
        </Table>

        <hr className="border-t border-gray-100 pt-4" />

        <Pagination
          total={candidateData?.meta.total || 0}
          current={page}
          pageSize={LIMIT}
          defaultCurrent={1}
          showLessItems={true}
          onChange={(page: number) => setPage(page)}
          prevIconClassName="py-0 text-gray-500 !leading-[26px]"
          nextIconClassName="py-0 text-gray-500 !leading-[26px]"
          className="justify-center [&>.rc-pagination-item-active>a]:!text-white [&>.rc-pagination-item-active]:!border-primary [&>.rc-pagination-item-active]:!bg-primary [&>.rc-pagination-item-active]:!text-white [&>.rc-pagination-item:not(.rc-pagination-item-active)>a]:text-black [&>.rc-pagination-item:not(.rc-pagination-item-active)]:bg-transparent [&>.rc-pagination-item]:rounded-lg"
          variant="solid"
        />
      </div>
    </div>
  );
}
