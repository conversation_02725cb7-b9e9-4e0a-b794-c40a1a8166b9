import { API_ENDPONTS } from '@/config/endpoint';
import { requestGet } from '@/utils/http-client';
import { GetOrgHomeJobsResponse, JobQueryKeys } from './types';

import {
  useInfiniteQuery,
  useQuery,
  useQueryClient,
} from '@tanstack/react-query';

export async function getOrgHomeJobs(params: {
  orgId: string;
  limit: number;
  keyword?: string;
  lastId?: string;
  isFeatured?: boolean;
}): Promise<GetOrgHomeJobsResponse> {
  const { orgId, limit, keyword, lastId, isFeatured = false } = params;

  const response = await requestGet<GetOrgHomeJobsResponse>(
    API_ENDPONTS.GET_ORG_PUBLIC_JOBS,
    {
      limit,
      ...(keyword && { keyword }),
      ...(lastId && { lastId }),
      isFeatured,
    },
    {
      orgId,
    }
  );
  return response.data;
}

export function useGetOrgFeaturedSimulations(params: { orgId: string }) {
  const query = useQuery<GetOrgHomeJobsResponse>({
    queryKey: [JobQueryKeys.GET_ORG_FEATURED_JOBS, params.orgId],
    queryFn: () =>
      getOrgHomeJobs({
        limit: 6,
        orgId: params.orgId,
        isFeatured: true,
      }),
    enabled: !!params.orgId,
  });
  const jobs = query.data?.data || [];

  return {
    ...query,
    jobs,
  };
}

export function useGetOrgHomeJobsInfinite(params: {
  orgId: string;
  keyword?: string;
}) {
  const queryClient = useQueryClient();

  const query = useInfiniteQuery<GetOrgHomeJobsResponse, Error>({
    queryKey: [JobQueryKeys.GET_ORG_HOME_JOBS, params.orgId, params.keyword],
    queryFn: async ({ pageParam }) => {
      const result = await getOrgHomeJobs({
        ...params,
        limit: 6,
        lastId: (pageParam as string | undefined) || '',
      });
      return result;
    },
    initialPageParam: undefined,
    enabled: !!params.orgId,
    getNextPageParam: (lastPage) =>
      lastPage.meta.hasMore && lastPage.meta.lastId
        ? lastPage.meta.lastId
        : undefined,
    refetchOnWindowFocus: false,
  });

  const jobs = query.data?.pages.flatMap((page) => page.data) ?? [];

  const updateJobStatus = (
    jobId: string,
    newStatus: 'active' | 'completed'
  ) => {
    queryClient.setQueryData(
      [JobQueryKeys.GET_ORG_HOME_JOBS, params.orgId, params.keyword],
      (oldData: any) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          pages: oldData.pages.map((page: any) => ({
            ...page,
            data: page.data.map((job: any) =>
              job.jobId === jobId
                ? { ...job, progress: { status: newStatus } }
                : job
            ),
          })),
        };
      }
    );

    queryClient.setQueryData(
      [JobQueryKeys.GET_ORG_FEATURED_JOBS, params.orgId],
      (oldData: GetOrgHomeJobsResponse | undefined) => {
        console.log('oldData ::: ', oldData);
        if (!oldData) return oldData;
        return {
          ...oldData,
          data: oldData.data.map((job) =>
            job.jobId === jobId
              ? { ...job, progress: { status: newStatus } }
              : job
          ),
        };
      }
    );
  };

  return {
    ...query,
    jobs,
    updateJobStatus,
  };
}
