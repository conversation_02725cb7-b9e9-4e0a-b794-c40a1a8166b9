'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import cn from '@/utils/class-names';

export default function PricingTestimonial() {
  const testimonials = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      role: 'Software Development Manager',
      location: 'Lansing, Illinois',
      avatar: '/pricing/brittni-lando.jpg',
      quote:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s",
    },
    {
      name: 'Tynisha Obey',
      role: 'Software Developer',
      location: 'Pasadena, Oklahoma',
      avatar: '/pricing/tynisha-obey.jpg',
      quote:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s",
    },
    {
      name: '<PERSON><PERSON>',
      role: 'Software Developer',
      location: 'Lafayette, California',
      avatar: '/pricing/kylee-danford.webp',
      quote:
        "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s",
    },
    {
      name: 'Liam Harper',
      role: 'Tech Recruiter',
      location: 'Austin, Texas',
      avatar: '/pricing/brittni-lando.jpg',
      quote:
        'Great experience with simulation hiring. Reduced our time-to-hire by half!',
    },
    {
      name: 'Sophia Zhang',
      role: 'HR Manager',
      location: 'Seattle, Washington',
      avatar: '/pricing/tynisha-obey.jpg',
      quote:
        "We've seen a massive improvement in candidate quality since switching.",
    },
    {
      name: 'Mohammed Khan',
      role: 'Lead Developer',
      location: 'San Diego, California',
      avatar: '/pricing/kylee-danford.webp',
      quote:
        'I was skeptical at first, but now I fully support simulation-based hiring.',
    },
    {
      name: 'Emily Chen',
      role: 'People Operations',
      location: 'New York, New York',
      avatar: '/pricing/brittni-lando.jpg',
      quote:
        'Candidates love the fairness and transparency of the simulation process.',
    },
    {
      name: 'Carlos Rivera',
      role: 'CTO',
      location: 'Miami, Florida',
      avatar: '/pricing/kylee-danford.webp',
      quote:
        'It’s helped our startup scale hiring efficiently without sacrificing quality.',
    },
  ];

  const itemsPerPage = 3;
  const totalPages = Math.ceil(testimonials.length / itemsPerPage);
  const [page, setPage] = useState(0);
  const [prevPage, setPrevPage] = useState(0);
  const direction =
    page > prevPage || (page === 0 && prevPage === totalPages - 1)
      ? 'next'
      : 'prev';

  const handlePageChange = (i: number) => {
    setPrevPage(page);
    setPage(i);
  };

  // Auto transition
  useEffect(() => {
    const interval = setInterval(() => {
      setPrevPage(page);
      setPage((prev) => (prev + 1) % totalPages);
    }, 6000);
    return () => clearInterval(interval);
  }, [page, totalPages]);

  const paginated = testimonials.slice(
    page * itemsPerPage,
    page * itemsPerPage + itemsPerPage
  );

  return (
    <div
      className="bg-cover bg-no-repeat py-20"
      style={{
        backgroundImage: "url('/pricing/testimonial.png')",
      }}
    >
      <div className="mx-auto max-w-[1200px] px-4 xl:px-0">
        {/* Heading */}
        <div className="mb-10 text-center">
          <h2 className="text-2xl font-bold text-white md:text-3xl">
            What Employers Are Saying
          </h2>
          <p className="mt-2 text-gray-200">
            See how companies are transforming their hiring with
            simulation-based recruitment
          </p>
        </div>

        {/* Slide container with fixed height */}
        <div className="relative h-[200px] overflow-hidden">
          <div
            key={page}
            className={cn(
              'absolute inset-0 grid transform grid-cols-1 gap-8 transition-all duration-700 ease-in-out md:grid-cols-3',
              direction === 'next'
                ? 'animate-slide-left'
                : 'animate-slide-right'
            )}
          >
            {paginated.map((item, index) => (
              <div
                key={index}
                className="relative flex min-h-[200px] flex-col rounded-2xl bg-white p-6 shadow-md"
              >
                <div className="mb-4 flex items-center gap-4">
                  <Image
                    src={item.avatar}
                    alt={item.name}
                    className="h-12 w-12 rounded-full border-4 border-[#1696FF] object-cover"
                    width={48}
                    height={48}
                    loader={({ src }) => src}
                  />
                  <div>
                    <h4 className="text-sm font-bold">{item.name}</h4>
                    <p className="text-xs text-gray-600">{item.role}</p>
                    <p className="text-xs text-gray-400">{item.location}</p>
                  </div>
                </div>
                <div className="mb-6 flex-grow">
                  <p className="line-clamp-4 text-sm italic text-gray-700">
                    “{item.quote}”
                  </p>
                </div>
                <div className="absolute right-3 top-3 text-[100px] leading-none text-gray-500">
                  ”
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Pagination */}
        <div className="mt-10 flex justify-center gap-2">
          {Array.from({ length: totalPages }).map((_, i) => (
            <button
              key={i}
              onClick={() => handlePageChange(i)}
              className={cn(
                'h-2 rounded-full transition-all duration-300',
                page === i
                  ? 'bg-primary w-6'
                  : 'w-2 bg-white opacity-40 hover:opacity-70'
              )}
            />
          ))}
        </div>
      </div>

      {/* Animation styles */}
      <style jsx>{`
        @keyframes slideLeft {
          from {
            transform: translateX(100%);
            opacity: 0.3;
          }
          to {
            transform: translateX(0%);
            opacity: 1;
          }
        }
        @keyframes slideRight {
          from {
            transform: translateX(-100%);
            opacity: 0.3;
          }
          to {
            transform: translateX(0%);
            opacity: 1;
          }
        }
        .animate-slide-left {
          animation: slideLeft 0.6s ease forwards;
        }
        .animate-slide-right {
          animation: slideRight 0.6s ease forwards;
        }
      `}</style>
    </div>
  );
}
