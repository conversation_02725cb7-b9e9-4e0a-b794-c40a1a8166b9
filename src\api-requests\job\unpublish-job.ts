import axiosInstance from '@/utils/http-client';
import { JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function unpublishJob(payload: { jobId: string; orgId: string }) {
  const response = await axiosInstance.post(
    API_ENDPONTS.UNPUBLISH_JOB.replace(':jobId', payload.jobId),
    { orgId: payload.orgId }
  );
  return response.data;
}

export const useUnpublishJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { jobId: string; orgId: string }) =>
      unpublishJob(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.UNPUBLISH_JOB],
      });
    },
  });
};
