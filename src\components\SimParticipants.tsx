'use client';

import { HomeSimulationCandidate } from '@/api-requests/simulation';
import cn from '@/utils/class-names';

interface ParticipantsProps {
  candidates: HomeSimulationCandidate[];
  total: number;
  avatarClassName?: string;
}

const formatNumber = (num: number): string => {
  if (num >= 1_000_000) {
    return (num / 1_000_000).toFixed(1).replace(/\.0$/, '') + 'M';
  }
  if (num >= 1_000) {
    return (num / 1_000).toFixed(1).replace(/\.0$/, '') + 'K';
  }
  return num.toString();
};

const SimParticipants = ({
  candidates,
  total,
  avatarClassName,
}: ParticipantsProps) => {
  const visibleAvatars = candidates.slice(0, 3);
  let remaining = total - visibleAvatars.length;
  if (visibleAvatars.length < 3) {
    remaining = 0;
  }

  return (
    <div className="flex items-center space-x-3">
      <div className="flex -space-x-3">
        {visibleAvatars.map((c, i) => (
          <img
            key={i}
            src={c.avatar || '/avatar/user-default.png'}
            onError={(e) => {
              e.currentTarget.src = '/avatar/user-default.png';
            }}
            alt={c.name}
            className={cn(
              'h-6 w-6 rounded-full border-2 border-white object-cover',
              avatarClassName
            )}
          />
        ))}
      </div>
      {remaining > 0 && (
        <span className="text-sm font-medium text-gray-800">
          +{formatNumber(remaining)} participants
        </span>
      )}
    </div>
  );
};

export default SimParticipants;
