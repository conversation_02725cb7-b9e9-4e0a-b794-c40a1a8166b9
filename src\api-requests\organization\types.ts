export const OrganizationQueryKeys = {
  GET_ORG_BY_OWNER: 'getOrgByOwner',
  GET_ORG_BY_ID: 'getOrgById',
  GET_ORG: 'getOrg',
  UPDATE_COMPANY: 'updateCompany',
};

export enum OrganizationType {
  COMPANY = 'company',
  EDUCATION = 'education',
  COMMUNITY = 'community',
}

export interface GetOrgParams {
  type: string;
  name?: string;
  skip?: number;
  limit?: number;
}

export interface Organization {
  _id: string;
  name: string;
  type: OrganizationType;
  logo: string;
  banner?: string;
  ownerId: string;
  description?: string;
  country: string;
  location: string;
  city?: string;
  region?: string;
  address?: string;
  website?: string;
  email?: string;
}
