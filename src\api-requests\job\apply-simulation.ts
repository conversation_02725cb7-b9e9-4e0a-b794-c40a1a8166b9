import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useMutation, UseMutationOptions } from '@tanstack/react-query';
import { AxiosError } from 'axios';

export async function applySimulation(params: {
  jobId?: string;
  simulationId: string;
}): Promise<string> {
  const response = await axiosInstance.post<{ success: boolean; link: string }>(
    API_ENDPONTS.APPLY_SIMULATION,
    params
  );
  return response.data.link || '';
}

export const useApplySimulation = (
  options?: UseMutationOptions<
    any,
    AxiosError,
    { jobId?: string; simulationId: string }
  >
) => {
  return useMutation({
    mutationFn: (params: { jobId?: string; simulationId: string }) =>
      applySimulation(params),
    ...options,
  });
};
