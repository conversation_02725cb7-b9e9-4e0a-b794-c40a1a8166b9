import axiosInstance from '@/utils/http-client';
import { Job, JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useQuery } from '@tanstack/react-query';

export async function getJob(jobId: string): Promise<Job> {
  const response = await axiosInstance.get<Job>(
    API_ENDPONTS.GET_BY_JOBID.replace(':jobId', jobId)
  );
  return response.data;
}

export function useGetJob(jobId: string) {
  return useQuery<Job>({
    queryKey: [JobQueryKeys.GET_BY_JOBID, jobId],
    queryFn: () => getJob(jobId),
    enabled: !!jobId,
  });
}
