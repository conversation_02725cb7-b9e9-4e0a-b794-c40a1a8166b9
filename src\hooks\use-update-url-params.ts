/* eslint-disable @typescript-eslint/no-explicit-any */

'use client';

import { AppRouterInstance } from 'next/dist/shared/lib/app-router-context.shared-runtime';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';

export interface URLParamsConfig {
  [key: string]: string | string[] | number | boolean | null | undefined;
}

/**
 * Cập nhật URL search params
 * @param router - Next.js router instance
 * @param pathname - Current pathname
 * @param currentParams - Current URLSearchParams
 * @param newParams - Object chứa params cần cập nhật
 * @param options - Cấu hình thêm
 */
export function updateURLParams(
  router: AppRouterInstance,
  pathname: string,
  currentParams: URLSearchParams,
  newParams: URLParamsConfig,
  options: {
    scroll?: boolean;
    replace?: boolean;
  } = {}
) {
  const { scroll = false, replace = true } = options;
  const searchParams = new URLSearchParams(currentParams.toString());

  // Xử lý từng param
  Object.entries(newParams).forEach(([key, value]) => {
    if (value === undefined) {
      // Không thay đổi nếu value là undefined
      return;
    }

    if (
      value === null ||
      value === '' ||
      (Array.isArray(value) && value.length === 0)
    ) {
      // Xóa param nếu value rỗng
      searchParams.delete(key);
    } else if (Array.isArray(value)) {
      // Xử lý array - join bằng dấu phẩy
      searchParams.set(key, value.join(','));
    } else if (typeof value === 'number') {
      // Xử lý number
      if (value > 0) {
        searchParams.set(key, value.toString());
      } else {
        searchParams.delete(key);
      }
    } else if (typeof value === 'boolean') {
      // Xử lý boolean
      if (value) {
        searchParams.set(key, 'true');
      } else {
        searchParams.delete(key);
      }
    } else {
      // Xử lý string
      searchParams.set(key, value.toString());
    }
  });

  // Tạo URL mới
  const newURL = searchParams.toString()
    ? `${pathname}?${searchParams.toString()}`
    : pathname;

  // Cập nhật URL
  if (replace) {
    router.replace(newURL, { scroll });
  } else {
    router.push(newURL, { scroll });
  }
}

/**
 * Đọc params từ URL và parse thành object
 * @param searchParams - URLSearchParams từ useSearchParams()
 * @param schema - Schema định nghĩa kiểu dữ liệu cho từng param
 */
export function parseURLParams<T extends Record<string, any>>(
  searchParams: URLSearchParams,
  schema: {
    [K in keyof T]: 'string' | 'number' | 'boolean' | 'string[]';
  }
): Partial<T> {
  const result: Partial<T> = {};

  Object.entries(schema).forEach(([key, type]) => {
    const value = searchParams.get(key);

    if (value === null) {
      return;
    }

    switch (type) {
      case 'string':
        result[key as keyof T] = value as T[keyof T];
        break;
      case 'number':
        const numValue = parseInt(value);
        if (!isNaN(numValue)) {
          result[key as keyof T] = numValue as T[keyof T];
        }
        break;
      case 'boolean':
        result[key as keyof T] = (value === 'true') as T[keyof T];
        break;
      case 'string[]':
        result[key as keyof T] = (value ? value.split(',') : []) as T[keyof T];
        break;
    }
  });

  return result;
}

/**
 * Tạo URL mới với params bổ sung mà không thay đổi params hiện tại
 * @param pathname - Current pathname
 * @param currentParams - Current URLSearchParams
 * @param additionalParams - Object chứa params cần thêm vào
 */
export function createURLWithAdditionalParams(
  pathname: string,
  currentParams: URLSearchParams,
  additionalParams: URLParamsConfig
): string {
  // Tạo copy của current params để không làm thay đổi original
  const searchParams = new URLSearchParams(currentParams.toString());

  // Thêm các params mới
  Object.entries(additionalParams).forEach(([key, value]) => {
    if (value === undefined || value === null) {
      return;
    }

    if (Array.isArray(value)) {
      if (value.length > 0) {
        searchParams.set(key, value.join(','));
      }
    } else if (typeof value === 'number') {
      searchParams.set(key, value.toString());
    } else if (typeof value === 'boolean') {
      searchParams.set(key, value.toString());
    } else {
      searchParams.set(key, value.toString());
    }
  });

  // Tạo URL mới
  return searchParams.toString()
    ? `${pathname}?${searchParams.toString()}`
    : pathname;
}

/**
 * Custom hook để quản lý URL params một cách tự động
 */
export function useURLParamsManager() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  return {
    /**
     * Cập nhật một hoặc nhiều params
     */
    update: (
      params: URLParamsConfig,
      options?: { scroll?: boolean; replace?: boolean }
    ) => {
      updateURLParams(router, pathname, searchParams, params, options);
    },

    /**
     * Xóa một hoặc nhiều params
     */
    remove: (keys: string | string[]) => {
      const keysArray = Array.isArray(keys) ? keys : [keys];
      const paramsToRemove = keysArray.reduce((acc, key) => {
        acc[key] = null;
        return acc;
      }, {} as URLParamsConfig);

      updateURLParams(router, pathname, searchParams, paramsToRemove);
    },

    /**
     * Parse params theo schema
     */
    parse: <T extends Record<string, any>>(schema: {
      [K in keyof T]: 'string' | 'number' | 'boolean' | 'string[]';
    }) => {
      return parseURLParams<T>(searchParams, schema);
    },

    /**
     * Các helper methods thêm
     */
    get: (key: string) => searchParams.get(key),
    getAll: () => Object.fromEntries(searchParams.entries()),
    has: (key: string) => searchParams.has(key),
    clear: () => {
      const allKeys = Array.from(searchParams.keys());
      const paramsToRemove = allKeys.reduce((acc, key) => {
        acc[key] = null;
        return acc;
      }, {} as URLParamsConfig);
      updateURLParams(router, pathname, searchParams, paramsToRemove);
    },
    copy: (additionalParams: URLParamsConfig): string => {
      return createURLWithAdditionalParams(
        pathname,
        searchParams,
        additionalParams
      );
    },
  };
}
