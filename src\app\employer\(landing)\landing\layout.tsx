import cn from '@/utils/class-names';
import EmployerBanner from '@/views/banner/employer';
import EmployerFooter from '@/views/employer-footer';

export default function LayoutProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      <EmployerBanner />

      <div
        className={cn(
          'relative z-10 mx-auto w-full max-w-[1200px] px-4 py-20 xl:px-0'
        )}
      >
        {children}
      </div>

      <EmployerFooter />
    </>
  );
}
