'use client';

import { use<PERSON>tom } from 'jotai';
import EmployerInfo from './employer-info';
import { userAtom } from '@/store/user-atom';
import { useGetUserProfile, UserProfile } from '@/api-requests/user-profile';
import cn from '@/utils/class-names';
import { Loader } from 'rizzui';
import { useState } from 'react';
import ProfileDetailModal from '../user/profile-detail-modal';

export default function EmployerProfile() {
  const [user] = useAtom(userAtom);
  const [openEdit, setOpenEdit] = useState(false);

  const { data, isLoading, refetch } = useGetUserProfile(user?.id as string);

  return (
    <>
      <div
        className={cn(
          'rounded-[20px] p-6 px-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)] transition-all duration-300 ease-in-out sm:px-6 lg:px-8',
          isLoading && 'blur-[3px]'
        )}
      >
        {isLoading ? (
          <div className="flex h-[272px] items-center justify-center">
            <Loader />
          </div>
        ) : (
          <EmployerInfo
            profile={data as UserProfile}
            user={user}
            onEditProfile={() => setOpenEdit(true)}
          />
        )}
      </div>

      {openEdit && (
        <ProfileDetailModal
          open={openEdit}
          onClose={() => setOpenEdit(false)}
          refetch={refetch}
          profile={data as UserProfile}
          user={user}
        />
      )}
    </>
  );
}
