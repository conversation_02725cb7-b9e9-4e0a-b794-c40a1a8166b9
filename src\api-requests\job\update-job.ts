import axiosInstance from '@/utils/http-client';
import { Job, JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function updateJob(
  jobId: string,
  params: Job
): Promise<Job | any> {
  try {
    const response = await axiosInstance.put<Job>(
      API_ENDPONTS.GET_BY_JOBID.replace(':jobId', jobId),
      params
    );
    return response.data;
  } catch (error: any) {
    return {
      isSuccess: false,
      message: error?.response?.data?.message,
    };
  }
}

export const useUpdateJob = (jobId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (params: Job) => updateJob(jobId, params),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.UPDATE_JOB],
      });
    },
  });
};
