'use client';

import { Switch } from 'rizzu<PERSON>';
import ArrowLeftIcon from '../icons/arrow-left';
import { Stat } from '../candidate-list/job-card';
import { ArrowUpRightIcon } from 'lucide-react';
import JobDetailTabs from './job-detail-tabs';
import { useParams, useRouter } from 'next/navigation';
import { useGetJob } from '@/api-requests/job/get-by-id';
import { Job } from '@/api-requests/job';
import { useUpdateJob } from '@/api-requests/job/update-job';
import { useEffect, useState } from 'react';
import toast from 'react-hot-toast';

export default function JobDetail() {
  const router = useRouter();
  const { id } = useParams();

  const [cvSwitch, setCvSwitch] = useState(false);
  const [simulationSwitch, setSimulationSwitch] = useState(false);
  const [candidateInfo, setCandidateInfo] = useState({
    totalCandidates: 0,
    matchedCandidates: 0,
    averageMatch: 0,
  });

  const { data: job, isLoading } = useGetJob(id as string);
  const { mutateAsync, isPending } = useUpdateJob(id as string);

  useEffect(() => {
    if (job?.applyMode) {
      setCvSwitch(['all', 'cv'].includes(job?.applyMode as string));
      setSimulationSwitch(
        ['all', 'simulation'].includes(job?.applyMode as string)
      );
    }
  }, [job?.applyMode]);

  const handleApplyModeChange = async (
    cvEnabled: boolean,
    simulationEnabled: boolean
  ) => {
    const previousCvSwitch = cvSwitch;
    const previousSimulationSwitch = simulationSwitch;

    let applyMode: string[] = [];

    if (cvEnabled && simulationEnabled) {
      applyMode = ['cv', 'simulation'];
    } else if (cvEnabled) {
      applyMode = ['cv'];
    } else if (simulationEnabled) {
      applyMode = ['simulation'];
    }

    try {
      const resp = await mutateAsync({
        applyMode,
      } as Job);

      if (resp && resp.isSuccess !== false) {
        toast.success('Update apply mode successfully');
      } else {
        toast.error('Update apply mode failed');
        setCvSwitch(previousCvSwitch);
        setSimulationSwitch(previousSimulationSwitch);
      }
    } catch (error) {
      setCvSwitch(previousCvSwitch);
      setSimulationSwitch(previousSimulationSwitch);
      toast.error('Update apply mode failed');
    }
  };

  const handleCvChange = (checked: boolean) => {
    setCvSwitch(checked);
    handleApplyModeChange(checked, simulationSwitch);
  };

  const handleSimulationChange = (checked: boolean) => {
    setSimulationSwitch(checked);
    handleApplyModeChange(cvSwitch, checked);
  };

  return (
    <div className="space-y-6">
      <button
        className="flex items-center gap-2 pb-4"
        onClick={() => router.push('/org/admin/jobs')}
      >
        <ArrowLeftIcon className="h-6 w-6" />
        <span>Job details</span>
      </button>

      <div className="flex flex-col gap-6 sm:flex-row">
        <div className="w-full space-y-4 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)] sm:w-[60%]">
          <div>{job?.title}</div>
          <div>
            <Switch
              variant="flat"
              label={
                <div>
                  <span>Apply via CV</span>
                  <span className="pl-1 text-[13px] text-gray-400">
                    (Allow candidates to apply using their CV)
                  </span>
                </div>
              }
              checked={cvSwitch}
              onChange={(e) => handleCvChange(e.target.checked)}
              disabled={job?.isPublished || isPending || isLoading}
            />
            <Switch
              variant="flat"
              label={
                <div>
                  <span>Job Simulation</span>
                  <span className="pl-1 text-[13px] text-gray-400">
                    (Allow candidates to apply by taking a job simulation test)
                  </span>
                </div>
              }
              checked={simulationSwitch}
              onChange={(e) => handleSimulationChange(e.target.checked)}
              disabled={job?.isPublished || isPending || isLoading}
            />
          </div>
        </div>
        <div className="flex w-full gap-6 rounded-lg bg-white p-4 shadow-[0_4px_30px_rgba(0,0,0,0.15)] sm:w-[60%]">
          <Stat label="Candidates" value={candidateInfo.totalCandidates} />
          <Stat label="Candidate matches" value={candidateInfo.matchedCandidates} />
          <Stat label="Avg Match" value={candidateInfo.averageMatch + "%"} />
          <div className="text-center">
            <div className="text-sm text-gray-500">Simulation</div>
            <div className="mt-1 flex w-[120px] items-center justify-between rounded-full bg-gray-100 px-3 py-1 text-sm">
              <span className="truncate">Software...</span>
              <ArrowUpRightIcon className="h-5 w-5 opacity-70" />
            </div>
          </div>
        </div>
      </div>

      <JobDetailTabs job={job as Job} jobId={id as string} setCandidateInfo={setCandidateInfo} />
    </div>
  );
}
