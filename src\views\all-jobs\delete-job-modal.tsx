'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal, Title } from 'rizzu<PERSON>';
import CloseIcon from '../icons/close';
import { useDeleteJob } from '@/api-requests/job/delete-job';
import { Job } from '@/api-requests/job';
import toast from 'react-hot-toast';

interface IProps {
  open: boolean;
  onClose: () => void;
  job: Job;
}

export default function DeleteJobModal({ open, onClose, job }: IProps) {
  const { isPending, mutateAsync } = useDeleteJob();

  const handleDeleteJob = async () => {
    if (!job?.jobId) return;
    const resp = await mutateAsync(job.jobId);

    if (resp === true) {
      toast.success('Job deleted successfully');
      onClose();
    } else {
      toast.error(resp?.message || 'Failed to delete job');
    }
  };

  return (
    <Modal isOpen={open} onClose={onClose} size="lg">
      <div className="w-full rounded-[20px] p-6 sm:w-[450px]">
        <div className="mb-5 flex items-center justify-between">
          <Title as="h4">Delete Job</Title>
          <ActionIcon size="sm" variant="text" onClick={onClose}>
            <CloseIcon className="h-auto w-6" strokeWidth={1.8} />
          </ActionIcon>
        </div>

        <div className="space-y-6">
          <div>
            <div className="font-semibold">
              Are you sure you want to delete this job?
            </div>
            <p className="text-sm text-gray-500">
              This action cannot be undone.
            </p>
          </div>

          <div className="flex justify-end space-x-4">
            <Button
              variant="outline"
              onClick={onClose}
              className="hover:bg-primary/10 border-primary text-primary"
            >
              Cancel
            </Button>
            <Button
              onClick={handleDeleteJob}
              className="hover:bg-primary/90 bg-primary text-white disabled:bg-gray-300 disabled:text-gray-500"
              disabled={isPending}
              isLoading={isPending}
            >
              Delete
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
