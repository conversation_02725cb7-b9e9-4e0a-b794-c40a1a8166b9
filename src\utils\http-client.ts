import { API_DOMAINS } from '@/config/endpoint';
import axios, { AxiosResponse } from 'axios';

const axiosInstance = axios.create({
  adapter: 'fetch',
  baseURL: API_DOMAINS.BASE_API,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 50000, // 50s
});

export const CAREER_AT_TOKEN = 'career_at';
export const CAREER_RT_TOKEN = 'career_rt';

export const getClientSideCookie = (name: string): string | undefined => {
  if (typeof document === 'undefined') {
    return undefined;
  }
  const cookieValue = document.cookie
    .split('; ')
    .find((row) => row.startsWith(`${name}=`))
    ?.split('=')[1];

  return cookieValue;
};

export const setClientSideCookie = (
  name: string,
  value: string,
  days: number
): void => {
  if (typeof document === 'undefined') {
    return;
  }
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  document.cookie = `${name}=${value}; expires=${date.toUTCString()}; path=/;`;
};

export const removeClientSideCookie = (name: string): void => {
  if (typeof document === 'undefined') {
    return;
  }
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
};

// Function to handle logout
const handleLogout = (isRedirect = true) => {
  removeClientSideCookie(CAREER_AT_TOKEN);
  removeClientSideCookie(CAREER_RT_TOKEN);

  if (typeof window !== 'undefined' && isRedirect) {
    setTimeout(() => {
      window.location.href = '/';
    }, 500);
  }
};

// Add a request interceptor
axiosInstance.interceptors.request.use(
  function (config) {
    const accessToken = getClientSideCookie(CAREER_AT_TOKEN);
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    // Do something before request is sent

    const countryName = getClientSideCookie('country-name');
    if (countryName) {
      config.headers['x-country-name'] = countryName;
    }
    return config;
  },
  function (error) {
    // Do something with request error
    return Promise.reject(error);
  }
);

// Add a response interceptor
axiosInstance.interceptors.response.use(
  function (response) {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    return response;
  },
  async function (error) {
    if (error.response && error.response.status === 401) {
      try {
        const original = error.config;
        const { data } = await axiosInstance.post(
          'api/auth/refresh',
          {},
          {
            baseURL: API_DOMAINS.BASE_URL,
            withCredentials: true,
          }
        );
        if (data?.shouldRedirect) {
          handleLogout();
          return Promise.reject({
            message: 'No refresh token found. Please login again.',
            originalError: error,
          });
        }
        const newAT = getClientSideCookie(CAREER_AT_TOKEN);
        if (!newAT) throw error;
        original.__isRetry = true;
        original.headers = {
          ...(original.headers || {}),
          Authorization: `Bearer ${newAT}`,
        };
        return axiosInstance(original);
      } catch {
        handleLogout(false);
        return Promise.reject({
          message: 'The login session has expired. Please login again.',
          originalError: error,
        });
      }
    }

    return Promise.reject(error);
  }
);

const buildURL = (url: string, params?: Record<string, any>) => {
  const pathParamKeys = Object.keys(params || {});
  let requestURL = url;
  pathParamKeys.forEach((key) => {
    if (url.includes(`/:${key}`) && params && params[key] !== undefined) {
      requestURL = requestURL.replace(`:${key}`, params[key]);
    }
  });

  return requestURL;
};

export const requestGet = async <T>(
  url: string,
  queries?: Record<string, any>,
  params?: Record<string, any>,
  config?: Record<string, any>
): Promise<AxiosResponse<T>> => {
  let requestURL = buildURL(url, params);
  const response = await axiosInstance.get<T>(requestURL, {
    params: queries,
    ...config,
  });
  return response;
};

export const requestPost = async <T>(
  url: string,
  data: any,
  params?: Record<string, any>,
  config?: Record<string, any>
): Promise<AxiosResponse<T>> => {
  let requestURL = buildURL(url, params);
  const response = await axiosInstance.post<T>(requestURL, data, config);
  return response;
};

export default axiosInstance;
