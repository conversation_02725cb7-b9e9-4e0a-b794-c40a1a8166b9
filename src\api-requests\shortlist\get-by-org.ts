import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';
import { useQuery } from '@tanstack/react-query';
import { Shortlist, ShortlistQueryKeys } from './types';

export async function getShorlistByOrg(orgId: string): Promise<Shortlist[]> {
  const response = await axiosInstance.get<Shortlist[]>(
    API_ENDPONTS.LIST_BY_ORG.replace(':orgId', orgId)
  );
  return response.data;
}

export function useGetShorlistByOrg(orgId: string) {
  return useQuery<Shortlist[]>({
    queryKey: [ShortlistQueryKeys.LIST_SHORTLIST_BY_ORG, orgId],
    queryFn: () => getShorlistByOrg(orgId),
    enabled: !!orgId,
  });
}
