import React from 'react';

function HeartOutlineIcon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="17"
      viewBox="0 0 16 17"
      {...props}
    >
      <path
        d="M2.82659 8.66676C2.56299 8.40491 2.35415 8.09322 2.21222 7.74985C2.07029 7.40648 1.99812 7.0383 1.99992 6.66676C1.99992 5.91531 2.29843 5.19464 2.82978 4.66329C3.36114 4.13194 4.08181 3.83343 4.83325 3.83343C5.88659 3.83343 6.80659 4.40676 7.29325 5.26009H8.03992C8.28733 4.82613 8.64532 4.46549 9.07745 4.21488C9.50957 3.96427 10.0004 3.83265 10.4999 3.83343C11.2514 3.83343 11.972 4.13194 12.5034 4.66329C13.0347 5.19464 13.3333 5.91531 13.3333 6.66676C13.3333 7.44676 12.9999 8.16676 12.5066 8.66676L7.66659 13.5001L2.82659 8.66676ZM12.9733 9.14009C13.6066 8.50009 13.9999 7.63343 13.9999 6.66676C13.9999 5.7385 13.6312 4.84826 12.9748 4.19189C12.3184 3.53551 11.4282 3.16676 10.4999 3.16676C9.33325 3.16676 8.29992 3.73343 7.66659 4.61343C7.34332 4.16443 6.91766 3.79897 6.4249 3.54738C5.93215 3.29579 5.38652 3.16531 4.83325 3.16676C3.90499 3.16676 3.01476 3.53551 2.35838 4.19189C1.702 4.84826 1.33325 5.7385 1.33325 6.66676C1.33325 7.63343 1.72659 8.50009 2.35992 9.14009L7.66659 14.4468L12.9733 9.14009Z"
        fill="currentColor"
      />
    </svg>
  );
}

export default HeartOutlineIcon;
