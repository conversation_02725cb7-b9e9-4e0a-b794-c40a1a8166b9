'use client';

import { authInitAtom, setUserAtom } from '@/store/user-atom';
import { useSetAtom } from 'jotai';
import { useEffect } from 'react';
import { UserInfo } from '@/store/user-atom';

export default function AuthProvider({
  children,
  session,
}: {
  children: React.ReactNode;
  session?: { user: UserInfo };
}) {
  // const init = useSetAtom(authInitAtom);
  const setUser = useSetAtom(setUserAtom);

  useEffect(() => {
    if (session) {
      setUser(session);
    }
  }, [setUser, session]);

  return <>{children}</>;
}
