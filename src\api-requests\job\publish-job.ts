import axiosInstance from '@/utils/http-client';
import { JobQueryKeys } from './types';
import { API_ENDPONTS } from '@/config/endpoint';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export async function publishJob(payload: { jobId: string; orgId: string }) {
  const response = await axiosInstance.post(
    API_ENDPONTS.PUBLISH_JOB.replace(':jobId', payload.jobId),
    { orgId: payload.orgId }
  );
  return response.data;
}

export const usePublishJob = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { jobId: string; orgId: string }) =>
      publishJob(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [JobQueryKeys.PUBLISH_JOB],
      });
    },
  });
};
