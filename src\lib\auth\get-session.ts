'use server';

import { API_DOMAINS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { headers } from 'next/headers';

export const getServerSession = async () => {
  try {
    const h = await headers();
    const cookie = h.get('cookie') ?? '';

    const { data } = await axiosInstance.get('/api/auth/session', {
      baseURL: API_DOMAINS.BASE_URL,
      withCredentials: true,
      headers: { cookie }, // forward cookie của request vào route
    });

    return data;
  } catch (error) {
    return null;
  }
};
