/* eslint-disable @typescript-eslint/no-explicit-any */
import { NextRequest, NextResponse } from 'next/server';
import { isProd, AT_COOKIE, RT_COOKIE, maxAgeDays } from '../../_utils';
import axiosInstance from '@/utils/http-client';
import { API_ENDPONTS } from '@/config/endpoint';

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { data } = await axiosInstance.post(
      API_ENDPONTS.SIGN_IN_PASSWORD,
      body
    );
    const { accessToken, refreshToken, user } = data || {};

    const res = NextResponse.json({ user });

    if (accessToken) {
      res.cookies.set(AT_COOKIE, accessToken, {
        httpOnly: false,
        sameSite: 'lax',
        secure: isProd,
        path: '/',
        maxAge: maxAgeDays(1),
      });
    }
    if (refreshToken) {
      res.cookies.set(RT_<PERSON>OKIE, refreshToken, {
        httpOnly: true,
        sameSite: 'lax',
        secure: isProd,
        path: '/',
        maxAge: maxAgeDays(7),
      });
    }

    return res;
  } catch (e: any) {
    return NextResponse.json(
      {
        message: e?.response?.data?.message || 'Something went wrong',
      },
      {
        status: e?.response?.data?.statusCode || 500,
      }
    );
  }
}
