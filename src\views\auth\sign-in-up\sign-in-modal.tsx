'use client';

import Image from 'next/image';
import { Modal } from 'rizzui';
import EmailForm from './email-form';
import { useCallback, useState } from 'react';
import PasswordForm from './password-form';
import { FormProvider, useForm } from 'react-hook-form';
import ResetPasswordForm from './reset-password-form';
import VerifyCodeForm from './verify-code-form';
import SignUpForm from './sign-up-form';
import CompanyForm from './company-form';
import { Role, UserInfo } from '@/store/user-atom';
import cn from '@/utils/class-names';

type FormValues = {
  firstName?: string;
  lastName?: string;
  email: string;
  password?: string;
  newPassword?: string;
  code?: string[];
  organizationName?: string;
  description?: string;
  address?: string;
  city?: string;
  region?: string;
  country?: string;
  file?: File | null;
  orgType: {
    type: string;
    role: string;
  };
  organization?: {
    label: string;
    value: string;
  } | null;
};

export const enum ActionType {
  VERIFY_CODE = 'verify_code',
  CONTINUE_WITH_PASSWORD = 'continue_with_password',
  SIGN_IN = 'sign_in',
  RESET_PASSWORD = 'reset_password',
  SIGN_UP = 'sign_up',
  COMPANY_SIGN_UP = 'company_sign_up',
}

export enum OrganizationType {
  COMPANY = 'company',
  Education = 'education',
  COMMUNITY = 'community',
}

interface IProps {
  open: boolean;
  onClose: () => void;
  onLoginSuccess?: (respUser?: UserInfo) => void;
  role: string;
}

export default function SignInModal({
  open,
  onClose,
  role,
  onLoginSuccess,
}: IProps) {
  const [actionType, setActionType] = useState<string>(
    ActionType.SIGN_IN
  );
  const [verifyType, setVerifyType] = useState<string>(ActionType.SIGN_UP);

  const methods = useForm<FormValues>({
    mode: 'onChange',
    defaultValues: {
      email: '',
      firstName: '',
      lastName: '',
      password: '',
      newPassword: '',
      code: Array(6).fill(''),
      organizationName: '',
      description: '',
      address: '',
      city: '',
      region: '',
      country: '',
      file: null,
      orgType: {
        type: OrganizationType.COMPANY,
        role: Role.EMPLOYER,
      },
      organization: null,
    },
  });

  const renderContent = useCallback(() => {
    switch (actionType) {
      case ActionType.CONTINUE_WITH_PASSWORD:
        return (
          <PasswordForm
            onSetAction={setActionType}
            onClose={onClose}
            onLoginSuccess={onLoginSuccess}
          />
        );
      case ActionType.VERIFY_CODE:
        return (
          <VerifyCodeForm
            onSetAction={setActionType}
            onClose={onClose}
            verifyType={verifyType}
            role={role}
          />
        );
      case ActionType.RESET_PASSWORD:
        return <ResetPasswordForm onSetAction={setActionType} />;
      case ActionType.SIGN_UP:
        return (
          <SignUpForm
            onSetAction={setActionType}
            onVerifyType={setVerifyType}
            role={role}
          />
        );
      case ActionType.COMPANY_SIGN_UP:
        return (
          <CompanyForm
            onSetAction={setActionType}
            onVerifyType={setVerifyType}
            role={role}
          />
        );
      default:
        return (
          <EmailForm
            onSetAction={setActionType}
            onVerifyType={setVerifyType}
            onLoginSuccess={onLoginSuccess}
          />
        );
    }
  }, [actionType, role, onClose, setActionType, setVerifyType, verifyType]);

  return (
    <Modal
      isOpen={open}
      onClose={onClose}
      size="lg"
      customSize={actionType === ActionType.COMPANY_SIGN_UP ? '800px' : '450px'}
    >
      <div
        className={cn(
          'scrollbar-hide max-h-[80vh] w-full overflow-y-auto rounded-[20px] p-8',
          actionType === ActionType.COMPANY_SIGN_UP
            ? 'sm:w-[800px]'
            : 'sm:w-[450px]'
        )}
        style={{
          msOverflowStyle: 'none',
          scrollbarWidth: 'none',
        }}
      >
        <div className="w-full space-y-6">
          {actionType !== ActionType.RESET_PASSWORD && (
            <div className="flex w-full justify-center">
              <Image
                src={'/ic-io-logo-light.png'}
                alt="Industry Connect Logo"
                width={0}
                height={48}
                className="h-12 w-auto"
                loader={({ src }) => src}
              />
            </div>
          )}

          <FormProvider {...methods}>{renderContent()}</FormProvider>
        </div>
      </div>
    </Modal>
  );
}
