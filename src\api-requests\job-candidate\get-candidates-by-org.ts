import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import {
  JobCandidate,
  JobCandidateQueryKeys,
  OrgCandidateListParams,
} from './types';
import { cleanQueryParams } from '@/utils/url';
import { ApiListResponse } from '../types';
import { ShortlistCandidate } from '../shortlist-candidate';

export async function getCandidatesByOrg(
  orgId: string,
  params: OrgCandidateListParams
): Promise<ApiListResponse<ShortlistCandidate>> {
  const reps = await axiosInstance.get<ApiListResponse<ShortlistCandidate>>(
    API_ENDPONTS.GET_CANDIDATES_BY_ORG.replace(':orgId', orgId || ''),
    {
      params: cleanQueryParams(params),
    }
  );
  return reps.data;
}

export function useGetCandidatesByOrg(
  orgId: string,
  params: OrgCandidateListParams
) {
  return useQuery<ApiListResponse<ShortlistCandidate>>({
    queryKey: [JobCandidateQueryKeys.GET_CANDIDATE_BY_JOB, params],
    queryFn: () => getCandidatesByOrg(orgId, params),
    enabled: !!orgId,
  });
}
