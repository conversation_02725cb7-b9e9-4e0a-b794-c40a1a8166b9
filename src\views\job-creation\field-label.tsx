'use client';

import { Tooltip } from 'rizzui';
import InfoIcon from '../icons/info';

export default function FieldLabel({
  title,
  content,
}: {
  title: string;
  content?: string;
}) {
  return (
    <label className="mb-1 flex items-center gap-2 text-sm">
      {title}
      {content && (
        <Tooltip content={content} size="sm" color="invert">
          <InfoIcon className="h-5 w-5 text-gray-400" />
        </Tooltip>
      )}
    </label>
  );
}
