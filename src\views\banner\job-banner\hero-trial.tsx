'use client';

import UploadIcon from '@/views/icons/upload';
import React, { useState } from 'react';
import { Button } from 'rizzui';
import UploadCvModal from './upload-cv-modal';
import Image from 'next/image';

type HeroTrialProps = {
  primaryColor?: string; // ví dụ: '#0D1321'
  trialsThisMonth?: string; // '9,200 trials this month'
  interviewRate?: string; // '61% received interviews'
  medianDays?: string; // 'median 8 days to first interview'
};

export default function HeroTrial() {
  const [openUploadCv, setOpenUploadCv] = useState(false);

  return (
    <>
      <div className="grid grid-cols-1 items-center gap-10 lg:grid-cols-2 lg:gap-14">
        <div>
          <div className="font-semibold tracking-tight">
            <span className="block text-3xl leading-[1.05] md:text-4xl">
              You Skip Ghosting And Get Fast
            </span>
            <span className="block text-3xl leading-[1.05] md:text-4xl">
              feedback.
            </span>
          </div>

          <p className="mt-4 max-w-2xl leading-8 text-gray-700">
            You complete a 5-minute trial task today and receive a clear score,
            a fit report, and next steps. Managers see your work, not just your
            buzzwords.
          </p>

          {/* CTAs */}
          <div className="mt-4 flex flex-col gap-4 sm:flex-row">
            <Button
              className="bg-primary text-white"
              size="lg"
              rounded="lg"
              onClick={() => setOpenUploadCv(true)}
            >
              <UploadIcon />
              <span className="ml-2">I upload my CV for a tailored trial</span>
            </Button>
          </div>

          {/* Stats */}
          <div className="mt-6 flex flex-col gap-5 text-sm text-gray-700 sm:flex-row sm:items-center">
            <div className="inline-flex items-center gap-3">
              <span className="inline-flex h-6 w-6 items-center justify-center rounded-full ring-1 ring-slate-300">
                {/* Pulse/Waves icon */}
                <svg
                  viewBox="0 0 24 24"
                  className="h-3.5 w-3.5"
                  aria-hidden="true"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M2 12h3l2 7 4-14 3 7h8" />
                </svg>
              </span>
              <span>9,200 trials this month</span>
            </div>

            <div className="inline-flex items-center gap-3">
              <span className="inline-flex h-6 w-6 items-center justify-center rounded-full ring-1 ring-slate-300">
                {/* Users icon */}
                <svg
                  viewBox="0 0 24 24"
                  className="h-3.5 w-3.5"
                  aria-hidden="true"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <path d="M16 14a4 4 0 1 0-8 0" />
                  <circle cx="12" cy="7" r="3" />
                  <path d="M22 21a6 6 0 0 0-12 0" />
                </svg>
              </span>
              <span>61% received interviews</span>
            </div>

            <div className="inline-flex items-center gap-3">
              <span className="inline-flex h-6 w-6 items-center justify-center rounded-full ring-1 ring-slate-300">
                {/* Clock icon */}
                <svg
                  viewBox="0 0 24 24"
                  className="h-3.5 w-3.5"
                  aria-hidden="true"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                >
                  <circle cx="12" cy="12" r="9" />
                  <path d="M12 7v5l3 3" />
                </svg>
              </span>
              <span className="whitespace-nowrap">
                median 8 days to first interview
              </span>
            </div>
          </div>
        </div>

        {/* RIGHT */}
        <div className="w-full">
          <Image
            src="/job/overview.png"
            alt="overview"
            width={588}
            height={373}
            className="h-full w-full object-cover"
            loader={({ src }) => src}
          />
          {/* <div className="relative w-full rounded-3xl border-2 border-dashed border-[#D6DCE5] bg-white">
            <div className="flex aspect-video w-full items-center justify-center rounded-3xl">
              <div className="px-6 text-center">
                <div className="mx-auto mb-6 inline-flex h-16 w-16 items-center justify-center rounded-full ring-2 ring-slate-300">
                  <svg
                    viewBox="0 0 24 24"
                    className="h-6 w-6"
                    aria-hidden="true"
                    fill="currentColor"
                    style={{ color: '#9AA3B2' }}
                  >
                    <path d="M8 5.14v13.72a1 1 0 0 0 1.52.85l10.1-6.86a1 1 0 0 0 0-1.7L9.52 4.29A1 1 0 0 0 8 5.14Z" />
                  </svg>
                </div>

                <p className="text-base text-slate-500 sm:text-lg">
                  <span className="hidden sm:inline">
                    15s demo placeholder —{' '}
                  </span>
                  simulation flow (Search → Mini task → Score → Invite)
                </p>
              </div>
            </div>

            <div className="pointer-events-none absolute inset-0 rounded-3xl ring-1 ring-inset ring-slate-200/60"></div>
          </div> */}
        </div>
      </div>

      {openUploadCv && (
        <UploadCvModal
          open={openUploadCv}
          onClose={() => setOpenUploadCv(false)}
        />
      )}
    </>
  );
}
