import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ShortlistQueryKeys, UpdateParams, Shortlist } from './types';

export async function deleteShortlist(payload: {
  id: string;
  orgId: string;
}): Promise<Shortlist> {
  const reps = await axiosInstance.delete<Shortlist>(
    API_ENDPONTS.DELETE_SHORTLIST.replace(':id', payload.id).replace(
      ':orgId',
      payload.orgId
    )
  );
  return reps.data;
}

export const useDeleteShortlist = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (payload: { id: string; orgId: string }) =>
      deleteShortlist(payload),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [ShortlistQueryKeys.DELETE_SHORTLIST],
      });
    },
  });
};
