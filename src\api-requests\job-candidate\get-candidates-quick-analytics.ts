import { API_ENDPONTS } from '@/config/endpoint';
import axiosInstance from '@/utils/http-client';
import { useQuery } from '@tanstack/react-query';
import { JobCandidateQueryKeys, QuickAnalyticsResponse } from './types';

async function getData(params: {
  orgId: string;
}): Promise<QuickAnalyticsResponse> {
  const reps = await axiosInstance.get<QuickAnalyticsResponse>(
    API_ENDPONTS.GET_ORG_CANDIDATES_QUICK_ANALYTICS.replace(
      ':orgId',
      params.orgId || ''
    )
  );
  return reps.data;
}

export function useGetOrgCandidatesQuickAnalytics(params: { orgId: string }) {
  return useQuery<QuickAnalyticsResponse>({
    queryKey: [
      JobCandidateQueryKeys.GET_ORG_CANDIDATES_QUICK_ANALYTICS,
      params,
    ],
    queryFn: () => getData(params),
    enabled: !!params.orgId,
    retry: false,
  });
}
