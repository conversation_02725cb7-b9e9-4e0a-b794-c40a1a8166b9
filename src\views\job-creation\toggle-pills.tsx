'use client';

import PlusIcon from '../icons/plus';
import TickCircleIcon from '../icons/tick-circle';

type Option = { label: string; value: string };

export function TogglePills({
  options,
  value,
  onChange,
}: {
  options: Option[];
  value: string[];
  onChange: (next: string[]) => void;
}) {
  return (
    <div className="flex flex-wrap gap-4">
      {options.map((opt) => {
        const selected = value.includes(opt.value);
        const toggle = () => {
          onChange(
            selected
              ? value.filter((v) => v !== opt.value)
              : [...value, opt.value]
          );
        };

        return (
          <button
            key={opt.value}
            type="button"
            aria-pressed={selected}
            onClick={toggle}
            className={[
              'group inline-flex items-center gap-1 rounded-full px-2 py-1 transition-all',
              'border border-gray-200 bg-white shadow-sm',
              selected
                ? '!bg-primary text-white shadow-md'
                : 'hover:border-primary hover:text-primary hover:shadow-md',
            ].join(' ')}
          >
            <span
              className={[
                'inline-flex items-center justify-center rounded-full',
                selected
                  ? 'text-white'
                  : 'text-gray-600 group-hover:border-primary group-hover:text-primary',
              ].join(' ')}
            >
              <TickCircleIcon
                className={[
                  'h-4 w-4 transition-opacity',
                  selected
                    ? 'opacity-100'
                    : 'opacity-0 group-hover:opacity-100',
                ].join(' ')}
              />
              <PlusIcon
                className={[
                  'absolute h-4 w-4 transition-opacity',
                  selected ? 'opacity-0' : 'opacity-100 group-hover:opacity-0',
                ].join(' ')}
              />
            </span>

            <span>{opt.label}</span>
          </button>
        );
      })}
    </div>
  );
}
